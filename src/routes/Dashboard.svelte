<script lang="ts">
  import { invoke } from "@tauri-apps/api/core";
  import { onMount } from "svelte";
  import type { MemoryEntry, VaultStats, AgentPermissions, AgentQueryRequest, AgentQueryResponse } from '../lib/types';
  import Sidebar from "../components/Sidebar.svelte";
  import TopBar from "../components/TopBar.svelte";
  import AgentChat from "../lib/components/AgentChat.svelte";
  import AgentSettingsModal from '../lib/components/AgentSettingsModal.svelte';

  // ---------- STATE ----------
  let memoryContent = "";
  let memories: MemoryEntry[] = [];
  let isLoading = false;

  // AI Query
  let question = "";
  let aiResponse = "";
  let isQuerying = false;

  // Vault data
  let vaultStats: VaultStats | null = null;
  let allSources: string[] = [];
  let allTags: string[] = [];

  // Filters
  let selectedSource = "";
  let selectedTags: string[] = [];
  let searchAuthor = "";

  // Slack ingestion demo
  let showIngestionDemo = false;
  let slackChannel = "general";
  let slackAuthor = "john.doe";
  let slackMessage = "Just finished the project review - looking good!";

  // Agents
  let agents: AgentPermissions[] = [];
  let selectedAgent = "";
  let agentQuestion = "";
  let agentResponse: AgentQueryResponse | null = null;
  let isAgentQuerying = false;
  let showAgentManagement = false;
  let settingsAgent: AgentPermissions | null = null;

  // UI State
  let activeSection: 'memories' | 'agents' | 'explore' | 'integrations' | 'permissions' | 'system-integrations' = 'memories';

  // ---------- FUNCTIONS (unchanged from previous page) ----------
  async function saveMemory() {
    if (!memoryContent.trim()) {
      alert("Please enter some content");
      return;
    }
    isLoading = true;
    try {
      await invoke("save_memory", { content: memoryContent });
      memoryContent = "";
      await Promise.all([loadMemories(), loadVaultStats(), loadFilters()]);
    } finally {
      isLoading = false;
    }
  }

  async function loadMemories() {
    try {
      if (selectedSource || selectedTags.length > 0 || searchAuthor) {
        const request = {
          source_filter: selectedSource || null,
          tag_filter: selectedTags.length ? selectedTags : null,
          author_filter: searchAuthor || null,
          start_date: null,
          end_date: null,
        };
        memories = await invoke("search_memories", { request });
      } else {
        memories = await invoke("get_memories");
      }
    } catch (e) {
      memories = [];
    }
  }

  const loadVaultStats = async () => (vaultStats = await invoke("get_vault_stats"));
  const loadFilters = async () => {
    allSources = await invoke("get_sources");
    allTags = await invoke("get_tags");
  };
  const loadAgents = async () => (agents = await invoke("get_all_agents"));

  async function deleteMemory(memory: MemoryEntry) {
    await invoke("delete_memory_by_id", { id: memory.id });
    await loadMemories();
  }

  async function queryMemories() {
    if (!question.trim()) return;
    isQuerying = true;
    aiResponse = await invoke("query_memories", { question });
    isQuerying = false;
  }

  async function ingestSlackDemo() {
    if (!slackMessage.trim()) return;
    const request = {
      text: slackMessage,
      channel: slackChannel,
      author: slackAuthor,
      timestamp: null,
      thread_ts: null,
      message_type: "message",
    };
    await invoke("ingest_slack_memory", { request });
    slackMessage = "";
    await loadMemories();
  }

  function clearFilters() {
    selectedSource = "";
    selectedTags = [];
    searchAuthor = "";
    loadMemories();
  }

  const toggleTag = (tag: string) => {
    selectedTags = selectedTags.includes(tag)
      ? selectedTags.filter((t) => t !== tag)
      : [...selectedTags, tag];
    loadMemories();
  };

  const formatDate = (ts: string) => new Date(ts).toLocaleString();
  const getSourceIcon = (s: string) => ({ manual: "✏️", slack: "💬", email: "📧", git: "🔧" } as any)[s] || "📄";
  const getSourceBadgeColor = (s: string) => {
    switch (s) {
      case "manual":
        return "bg-gradient-to-r from-emerald-500 to-teal-600 text-white";
      case "slack":
        return "bg-gradient-to-r from-purple-500 to-indigo-600 text-white";
      case "email":
        return "bg-gradient-to-r from-blue-500 to-cyan-600 text-white";
      case "git":
        return "bg-gradient-to-r from-orange-500 to-red-600 text-white";
      default:
        return "bg-gradient-to-r from-gray-500 to-slate-600 text-white";
    }
  };

  // initial load
  onMount(async () => {
    await Promise.all([loadMemories(), loadVaultStats(), loadFilters(), loadAgents()]);
  });

  $: if (selectedSource || selectedTags.length || searchAuthor) loadMemories();
</script>

<!-- DASHBOARD WRAPPER -->
<div class="flex min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
  <Sidebar {activeSection} setSection={(s) => (activeSection = s as any)} />

  <div class="flex-1 flex flex-col overflow-hidden">
    <TopBar title="MindMesh" />

    <div class="relative flex-1 overflow-y-auto">
      <!-- decorative blobs -->
      <div class="fixed inset-0 pointer-events-none overflow-hidden">
        <div class="absolute -inset-10 opacity-20">
          <div class="absolute top-0 -left-4 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
          <div class="absolute top-0 -right-4 w-72 h-72 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
          <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
        </div>
      </div>

      <main class="relative z-10 p-6 max-w-7xl mx-auto space-y-10">
        <!-- HEADER with stats -->
        <section class="text-center">
          <h1 class="text-5xl md:text-6xl font-extrabold bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent mb-4">🧠 MindMesh</h1>
          {#if vaultStats}
            <div class="flex flex-wrap justify-center gap-4 text-sm">
              <div class="statBadge">{vaultStats.total_entries} memories</div>
              <div class="statBadge">{vaultStats.tags_count} tags</div>
              <div class="statBadge">Vault v{vaultStats.vault_version}</div>
            </div>
          {/if}
        </section>

        <!-- CONDITIONAL SECTIONS -->
        {#if activeSection === 'memories'}
          <!-- Memory Input & Query Grid -->
          <div class="grid grid-cols-1 xl:grid-cols-4 gap-8">
            <div class="xl:col-span-3 space-y-8">
              <!-- save memory -->
              <section class="panel">
                <h2 class="panelTitle">💾 Save a Memory</h2>
                <textarea rows="4" class="textarea" bind:value={memoryContent} placeholder="What would you like to remember?" />
                <button class="primaryBtn" disabled={isLoading} on:click={saveMemory}>
                  {isLoading ? '✨ Saving...' : 'Save Memory'}
                </button>
              </section>

              <!-- ask memories -->
              <section class="panel">
                <h2 class="panelTitle">🧠 Ask Your Memories</h2>
                <input class="input" bind:value={question} placeholder="What would you like to know?" on:keydown={(e)=> e.key==='Enter' && queryMemories()} />
                <button class="primaryBtn" on:click={queryMemories} disabled={isQuerying}>{isQuerying ? 'Thinking...' : 'Ask'}</button>
                {#if aiResponse}
                  <div class="responseBox">{aiResponse}</div>
                {/if}
              </section>

              <!-- ingestion demo toggle -->
              <section class="panel">
                <div class="flex justify-between items-center mb-6">
                  <h2 class="panelTitle">🔗 External Ingestion</h2>
                  <button class="secondaryBtn" on:click={() => (showIngestionDemo = !showIngestionDemo)}>
                    {showIngestionDemo ? 'Hide Demo' : 'Show Demo'}
                  </button>
                </div>
                {#if showIngestionDemo}
                  <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                      <input class="input" bind:value={slackChannel} placeholder="Channel" />
                      <input class="input" bind:value={slackAuthor} placeholder="Author" />
                    </div>
                    <textarea rows="3" class="textarea" bind:value={slackMessage} placeholder="Message content" />
                    <button class="primaryBtn" on:click={ingestSlackDemo}>📥 Ingest Slack Message</button>
                  </div>
                {/if}
              </section>
            </div>

            <!-- Filters sidebar -->
            <div class="space-y-6">
              <section class="panel p-6">
                <h3 class="text-xl font-semibold text-white mb-4">🔍 Filters</h3>
                <!-- source -->
                <label class="filterLabel">Source</label>
                <select class="select" bind:value={selectedSource}>
                  <option value="">All Sources</option>
                  {#each allSources as src}
                    <option value={src}>{src}</option>
                  {/each}
                </select>
                <!-- author -->
                <label class="filterLabel mt-4">Author</label>
                <input class="input" bind:value={searchAuthor} placeholder="Author..." />
                <!-- tags -->
                <label class="filterLabel mt-4">Tags</label>
                <div class="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                  {#each allTags as tag}
                    <button class="tagBtn {selectedTags.includes(tag) ? 'activeTag' : ''}" on:click={() => toggleTag(tag)}>#{tag}</button>
                  {/each}
                </div>
                {#if selectedSource || selectedTags.length || searchAuthor}
                  <button class="secondaryBtn w-full mt-4" on:click={clearFilters}>Clear Filters</button>
                {/if}
              </section>
            </div>
          </div>

          <!-- Memories list -->
          <section class="panel p-8">
            <h2 class="panelTitle mb-6">📚 Your Memories</h2>
            {#if memories.length === 0}
              <p class="text-center text-purple-200">No memories yet</p>
            {:else}
              <div class="space-y-4">
                {#each memories as m}
                  <div class="memoryCard">
                    <div class="flex flex-wrap gap-2 mb-2">
                      <span class="badge {getSourceBadgeColor(m.source)}">{getSourceIcon(m.source)} {m.source}</span>
                      {#each m.tags as t}
                        <span class="badge bg-yellow-600/30 text-yellow-300">#{t}</span>
                      {/each}
                    </div>
                    <p class="text-white mb-2">{m.content}</p>
                    <div class="text-xs text-purple-300 flex justify-between">
                      <span>👤 {m.author}</span>
                      <span>🕒 {formatDate(m.timestamp)}</span>
                    </div>
                    <button class="delBtn" on:click={() => deleteMemory(m)}>🗑️ Delete</button>
                  </div>
                {/each}
              </div>
            {/if}
          </section>
        {/if}

        {#if activeSection === 'agents'}
          <section class="panel p-8">
            <h2 class="panelTitle mb-6">🤖 Your Agents</h2>
            {#if agents.length === 0}
              <p class="text-center text-purple-200 mb-4">No agents found.</p>
              <button class="primaryBtn" on:click={async () => { await invoke('create_default_agents'); await loadAgents(); }}>Create Default Agents</button>
            {:else}
              <div class="space-y-6">
                {#each agents as agent}
                  <div class="bg-white/5 p-6 rounded-xl border border-white/10 relative flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div>
                      <div class="flex items-center gap-3 mb-1">
                        <span class="text-lg">{agent.enabled ? '🟢' : '🔴'}</span>
                        <span class="font-bold text-white text-lg">{agent.name}</span>
                        <span class="text-xs text-purple-300">({agent.agent_id})</span>
                      </div>
                      {#if agent.description}
                        <div class="text-purple-200 text-sm mb-1">{agent.description}</div>
                      {/if}
                      <div class="text-xs text-purple-300 flex flex-wrap gap-2">
                        <span>Sources: {agent.can_read_sources.join(', ')}</span>
                        <span>Tags: {agent.can_read_tags.join(', ')}</span>
                        <span>Authors: <AUTHORS>
                        {#if agent.max_memory_age_days}
                          <span>Max Age: {agent.max_memory_age_days} days</span>
                        {/if}
                      </div>
                    </div>
                    <div class="flex flex-col gap-2 min-w-[120px]">
                      <button class="primaryBtn" on:click={() => selectedAgent = agent.agent_id}>Chat</button>
                      <button class="secondaryBtn" on:click={() => settingsAgent = agent}>Settings</button>
                    </div>
                  </div>
                  {#if selectedAgent === agent.agent_id}
                    <div class="mt-4 mb-8">
                      <svelte:component this={AgentChat} agentId={agent.agent_id} agentName={agent.name} />
                      <button class="secondaryBtn mt-4" on:click={() => selectedAgent = ''}>Close Chat</button>
                    </div>
                  {/if}
                {/each}
              </div>
            {/if}
            {#if settingsAgent}
              <AgentSettingsModal
                agent={settingsAgent}
                allSources={allSources}
                allTags={allTags}
                onSave={async (updated) => {
                  await invoke('set_agent_permissions', { permissions: updated });
                  settingsAgent = null;
                  await loadAgents();
                }}
                onClose={() => settingsAgent = null}
              />
            {/if}
          </section>
        {/if}

        {#if activeSection === 'explore'}
          <p class="text-white">Explore section coming...</p>
        {/if}

        {#if activeSection === 'integrations'}
          <iframe src="/integrations" class="w-full h-screen border-0 rounded-lg" title="Web Integrations"></iframe>
        {/if}

        {#if activeSection === 'permissions'}
          <iframe src="/permissions" class="w-full h-screen border-0 rounded-lg" title="Permissions"></iframe>
        {/if}

        {#if activeSection === 'system-integrations'}
          <iframe src="/system-integrations" class="w-full h-screen border-0 rounded-lg" title="System Integrations"></iframe>
        {/if}
      </main>
    </div>
  </div>
</div>

<style>
  @keyframes blob {0%{transform:translate(0,0) scale(1);} 33%{transform:translate(30px,-50px) scale(1.1);} 66%{transform:translate(-20px,20px) scale(0.9);} 100%{transform:translate(0,0) scale(1);} }
  .animate-blob{animation:blob 7s infinite;}
  .animation-delay-2000{animation-delay:2s;}
  .animation-delay-4000{animation-delay:4s;}
  .panel{background:rgba(255,255,255,0.05);backdrop-filter:blur(20px);border:1px solid rgba(255,255,255,0.15);border-radius:1rem;padding:2rem;}
  .panelTitle{font-size:1.25rem;font-weight:600;color:white;margin-bottom:1rem;display:flex;gap:.5rem;align-items:center;}
  .input{width:100%;padding:1rem;background:rgba(255,255,255,0.05);border:1px solid rgba(255,255,255,0.15);border-radius:.75rem;color:white;}
  .textarea{ @apply input resize-none; }
  .primaryBtn{background:linear-gradient(to right,#8b5cf6,#ec4899);padding:.75rem 2rem;border-radius:.75rem;color:white;font-weight:bold;}
  .secondaryBtn{background:rgba(255,255,255,0.1);padding:.5rem 1rem;border:1px solid rgba(255,255,255,0.15);border-radius:.5rem;color:white;}
  .badge{padding:.25rem .75rem;border-radius:9999px;font-size:.75rem;display:inline-flex;align-items:center;gap:.25rem;}
  .statBadge{background:rgba(255,255,255,0.1);padding:.5rem 1rem;border-radius:9999px;color:white;}
  .select{ @apply input pr-8;}
  .filterLabel{ @apply block text-sm font-medium text-purple-200 mb-1;}
  .tagBtn{ @apply px-3 py-1 text-xs rounded-full border border-white/20 bg-white/10 text-purple-200;}
  .tagBtn.activeTag{background:#8b5cf6;color:white;border-color:#a78bfa;}
  .responseBox{ @apply mt-4 p-4 bg-white/5 rounded-lg text-white;}
  .memoryCard{ @apply bg-white/5 p-6 rounded-xl border border-white/10 relative;}
  .memoryCard .delBtn{ @apply absolute top-3 right-3 text-red-300 hover:text-red-200;}
</style> 