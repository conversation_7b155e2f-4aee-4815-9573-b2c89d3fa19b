<script lang="ts">
  import { onMount } from 'svelte';
  import { invoke } from '@tauri-apps/api/core';

  interface AppIntegration {
    id: string;
    app_type: any;
    name: string;
    description: string;
    system_permissions: any;
    connection_status: any;
    data_sources: any[];
    created_at: string;
  }

  interface SystemPermissionRequest {
    id: string;
    integration_id: string;
    app_name: string;
    app_type: any;
    requested_paths: string[];
    justification: string;
    status: string;
    created_at: string;
  }

  interface FileAccessInfo {
    path: string;
    file_type: string;
    size: number;
    last_modified: string;
    accessible: boolean;
  }

  let integrations: AppIntegration[] = [];
  let pendingRequests: SystemPermissionRequest[] = [];
  let selectedIntegration: AppIntegration | null = null;
  let scannedFiles: FileAccessInfo[] = [];
  let loading = true;
  let showCreateModal = false;
  let showFilesModal = false;
  let showPermissionModal = false;
  let selectedRequest: SystemPermissionRequest | null = null;

  // Create integration form
  let newIntegration = {
    app_type: 'notion',
    name: '',
    description: ''
  };

  const appTypes = [
    { value: 'notion', label: 'Notion', icon: '📝', description: 'Access local Notion data and exports' },
    { value: 'gmail', label: 'Gmail', icon: '📧', description: 'Read local email files and exports' },
    { value: 'calendar', label: 'Calendar', icon: '📅', description: 'Access calendar data and events' },
    { value: 'slack', label: 'Slack', icon: '💬', description: 'Read Slack logs and message history' },
    { value: 'github', label: 'GitHub', icon: '🐙', description: 'Access local Git repositories and files' }
  ];

  onMount(async () => {
    await loadData();
  });

  async function loadData() {
    try {
      loading = true;
      const [integrationsData, requestsData] = await Promise.all([
        invoke('get_system_integrations'),
        invoke('get_pending_system_requests')
      ]);
      
      integrations = integrationsData as AppIntegration[];
      pendingRequests = requestsData as SystemPermissionRequest[];
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      loading = false;
    }
  }

  async function createIntegration() {
    try {
      await invoke('create_system_integration', {
        appType: newIntegration.app_type,
        name: newIntegration.name,
        description: newIntegration.description
      });

      showCreateModal = false;
      newIntegration = { app_type: 'notion', name: '', description: '' };
      await loadData();
    } catch (error) {
      console.error('Failed to create integration:', error);
      alert('Failed to create integration: ' + error);
    }
  }

  async function requestSystemAccess(integration: AppIntegration) {
    try {
      await invoke('request_system_access', {
        integrationId: integration.id
      });
      await loadData();
    } catch (error) {
      console.error('Failed to request system access:', error);
      alert('Failed to request system access: ' + error);
    }
  }

  async function approveSystemAccess(request: SystemPermissionRequest) {
    try {
      await invoke('approve_system_access', {
        requestId: request.id
      });
      await loadData();
      showPermissionModal = false;
      selectedRequest = null;
    } catch (error) {
      console.error('Failed to approve access:', error);
      alert('Failed to approve access: ' + error);
    }
  }

  async function denySystemAccess(request: SystemPermissionRequest) {
    try {
      await invoke('deny_system_access', {
        requestId: request.id
      });
      await loadData();
      showPermissionModal = false;
      selectedRequest = null;
    } catch (error) {
      console.error('Failed to deny access:', error);
      alert('Failed to deny access: ' + error);
    }
  }

  async function scanFiles(integration: AppIntegration) {
    try {
      selectedIntegration = integration;
      const files = await invoke('scan_app_files', {
        integrationId: integration.id
      });
      scannedFiles = files as FileAccessInfo[];
      showFilesModal = true;
    } catch (error) {
      console.error('Failed to scan files:', error);
      alert('Failed to scan files: ' + error);
    }
  }

  function openPermissionModal(request: SystemPermissionRequest) {
    selectedRequest = request;
    showPermissionModal = true;
  }

  function getAppTypeInfo(appType: any) {
    const typeStr = typeof appType === 'string' ? appType : 
                   appType.Custom ? appType.Custom : 
                   Object.keys(appType)[0]?.toLowerCase();
    
    return appTypes.find(t => t.value === typeStr) || { value: typeStr, label: typeStr, icon: '🔗', description: 'Custom app integration' };
  }

  function getStatusColor(status: any) {
    const statusStr = typeof status === 'string' ? status : Object.keys(status)[0];
    switch (statusStr) {
      case 'Connected': return 'text-green-600 bg-green-100';
      case 'PermissionGranted': return 'text-blue-600 bg-blue-100';
      case 'PermissionRequested': return 'text-yellow-600 bg-yellow-100';
      case 'PermissionDenied': return 'text-red-600 bg-red-100';
      case 'NotConfigured': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  }

  function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  function isConnected(integration: AppIntegration): boolean {
    const statusStr = typeof integration.connection_status === 'string' ? 
      integration.connection_status : 
      Object.keys(integration.connection_status)[0];
    return statusStr === 'Connected' || statusStr === 'PermissionGranted';
  }
</script>

<div class="max-w-6xl mx-auto p-6">
  <div class="flex justify-between items-center mb-8">
    <div>
      <h1 class="text-3xl font-bold text-gray-900">System Integrations</h1>
      <p class="text-gray-600 mt-2">Connect to apps on your computer and give AI agents access to your local data</p>
    </div>
    <button 
      on:click={() => showCreateModal = true}
      class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
    >
      + Add Integration
    </button>
  </div>

  {#if loading}
    <div class="text-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
      <p class="text-gray-600 mt-4">Loading integrations...</p>
    </div>
  {:else}
    <!-- Permission Requests Section -->
    {#if pendingRequests.length > 0}
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-yellow-800 mb-4">
          🔔 System Permission Requests ({pendingRequests.length})
        </h2>
        <div class="space-y-4">
          {#each pendingRequests as request}
            <div class="bg-white border border-yellow-200 rounded-lg p-4">
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-2">
                    <span class="text-lg">{getAppTypeInfo(request.app_type).icon}</span>
                    <h3 class="font-semibold">{request.app_name}</h3>
                    <span class="text-sm text-gray-500">wants system access</span>
                  </div>
                  <p class="text-gray-700 mb-2">{request.justification}</p>
                  <div class="text-sm text-gray-600">
                    <strong>Requested paths:</strong>
                    <ul class="list-disc list-inside mt-1">
                      {#each request.requested_paths as path}
                        <li class="font-mono text-xs">{path}</li>
                      {/each}
                    </ul>
                  </div>
                </div>
                <div class="flex gap-2 ml-4">
                  <button 
                    on:click={() => openPermissionModal(request)}
                    class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
                  >
                    Review
                  </button>
                </div>
              </div>
            </div>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Integrations Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {#each integrations as integration}
        <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
          <div class="flex items-center gap-3 mb-4">
            <span class="text-2xl">{getAppTypeInfo(integration.app_type).icon}</span>
            <div>
              <h3 class="font-semibold text-lg">{integration.name}</h3>
              <p class="text-sm text-gray-600">{getAppTypeInfo(integration.app_type).label}</p>
            </div>
          </div>
          
          <p class="text-gray-700 mb-4">{integration.description}</p>
          
          <div class="flex items-center justify-between mb-4">
            <span class={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(integration.connection_status)}`}>
              {typeof integration.connection_status === 'string' ? integration.connection_status : Object.keys(integration.connection_status)[0]}
            </span>
            <span class="text-xs text-gray-500">
              {integration.data_sources.length} data source{integration.data_sources.length !== 1 ? 's' : ''}
            </span>
          </div>
          
          <div class="flex gap-2">
            {#if isConnected(integration)}
              <button 
                on:click={() => scanFiles(integration)}
                class="flex-1 bg-green-100 text-green-700 px-3 py-2 rounded hover:bg-green-200 transition-colors text-sm"
              >
                View Files
              </button>
            {:else}
              <button 
                on:click={() => requestSystemAccess(integration)}
                class="flex-1 bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 transition-colors text-sm"
              >
                Request Access
              </button>
            {/if}
          </div>
        </div>
      {/each}
    </div>

    {#if integrations.length === 0}
      <div class="text-center py-12">
        <div class="text-6xl mb-4">💻</div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">No system integrations yet</h3>
        <p class="text-gray-600 mb-6">Connect to apps on your computer to let AI agents access your local data</p>
        <button 
          on:click={() => showCreateModal = true}
          class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Add Your First Integration
        </button>
      </div>
    {/if}
  {/if}
</div>

<!-- Create Integration Modal -->
{#if showCreateModal}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md">
      <h2 class="text-xl font-semibold mb-4">Add System Integration</h2>
      
      <form on:submit|preventDefault={createIntegration} class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">App Type</label>
          <select bind:value={newIntegration.app_type} class="w-full border border-gray-300 rounded-lg px-3 py-2">
            {#each appTypes as appType}
              <option value={appType.value}>{appType.icon} {appType.label}</option>
            {/each}
          </select>
          <p class="text-xs text-gray-500 mt-1">
            {appTypes.find(t => t.value === newIntegration.app_type)?.description}
          </p>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
          <input 
            type="text" 
            bind:value={newIntegration.name}
            placeholder="My Notion Workspace"
            class="w-full border border-gray-300 rounded-lg px-3 py-2"
            required
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea 
            bind:value={newIntegration.description}
            placeholder="Personal workspace for project notes"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 h-20"
            required
          ></textarea>
        </div>
        
        <div class="flex gap-3 pt-4">
          <button 
            type="button"
            on:click={() => showCreateModal = false}
            class="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50"
          >
            Cancel
          </button>
          <button 
            type="submit"
            class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Create
          </button>
        </div>
      </form>
    </div>
  </div>
{/if}

<!-- Permission Request Modal -->
{#if showPermissionModal && selectedRequest}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
      <h2 class="text-xl font-semibold mb-4">System Permission Request</h2>
      
      <div class="space-y-4 mb-6">
        <div class="flex items-center gap-3">
          <span class="text-2xl">{getAppTypeInfo(selectedRequest.app_type).icon}</span>
          <div>
            <h3 class="font-semibold text-lg">{selectedRequest.app_name}</h3>
            <p class="text-gray-600">wants to access files on your computer</p>
          </div>
        </div>
        
        <div>
          <h4 class="font-medium mb-2">Justification:</h4>
          <p class="text-gray-700 bg-gray-50 p-3 rounded">{selectedRequest.justification}</p>
        </div>
        
        <div>
          <h4 class="font-medium mb-2">Requested File Paths:</h4>
          <div class="bg-gray-50 p-3 rounded">
            {#each selectedRequest.requested_paths as path}
              <div class="font-mono text-sm mb-1 p-2 bg-white rounded border">{path}</div>
            {/each}
          </div>
        </div>
        
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 class="font-medium text-yellow-800 mb-2">⚠️ Security Notice</h4>
          <p class="text-yellow-700 text-sm">
            This app is requesting access to files on your computer. Only approve if you trust this integration 
            and want AI agents to be able to read data from these locations.
          </p>
        </div>
      </div>
      
      <div class="flex gap-3">
        <button 
          on:click={() => { showPermissionModal = false; selectedRequest = null; }}
          class="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50"
        >
          Cancel
        </button>
        <button 
          on:click={() => denySystemAccess(selectedRequest)}
          class="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
        >
          Deny Access
        </button>
        <button 
          on:click={() => approveSystemAccess(selectedRequest)}
          class="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
        >
          Grant Access
        </button>
      </div>
    </div>
  </div>
{/if}

<!-- Files Modal -->
{#if showFilesModal && selectedIntegration}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">Files from {selectedIntegration.name}</h2>
        <button 
          on:click={() => { showFilesModal = false; selectedIntegration = null; scannedFiles = []; }}
          class="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>
      
      {#if scannedFiles.length === 0}
        <div class="text-center py-8">
          <div class="text-4xl mb-4">📁</div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No files found</h3>
          <p class="text-gray-600">No accessible files found in the configured paths</p>
        </div>
      {:else}
        <div class="space-y-2">
          {#each scannedFiles as file}
            <div class="border border-gray-200 rounded-lg p-3 hover:bg-gray-50">
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <div class="font-mono text-sm text-gray-800">{file.path}</div>
                  <div class="flex gap-4 text-xs text-gray-500 mt-1">
                    <span>Type: {file.file_type}</span>
                    <span>Size: {formatFileSize(file.size)}</span>
                    <span>Modified: {new Date(file.last_modified).toLocaleDateString()}</span>
                  </div>
                </div>
                <div class="ml-4">
                  {#if file.accessible}
                    <span class="text-green-600 text-xs">✓ Accessible</span>
                  {:else}
                    <span class="text-red-600 text-xs">✗ No access</span>
                  {/if}
                </div>
              </div>
            </div>
          {/each}
        </div>
        
        <div class="mt-4 text-sm text-gray-600 text-center">
          Found {scannedFiles.length} file{scannedFiles.length !== 1 ? 's' : ''}
        </div>
      {/if}
    </div>
  </div>
{/if}
