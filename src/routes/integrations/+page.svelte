<script lang="ts">
  import { onMount } from 'svelte';
  import { invoke } from '@tauri-apps/api/core';

  interface AppIntegration {
    id: string;
    app_type: any;
    name: string;
    description: string;
    connection_status: any;
    created_at: string;
    updated_at: string;
  }

  interface PermissionRequest {
    id: string;
    agent_id: string;
    agent_name: string;
    app_type: any;
    requested_permissions: string[];
    justification: string;
    status: string;
    created_at: string;
  }

  let integrations: AppIntegration[] = [];
  let pendingRequests: PermissionRequest[] = [];
  let loading = true;
  let showCreateModal = false;
  let showOAuthModal = false;
  let selectedIntegration: AppIntegration | null = null;

  // Create integration form
  let newIntegration = {
    app_type: 'notion',
    name: '',
    description: '',
    client_id: '',
    client_secret: ''
  };

  const appTypes = [
    { value: 'notion', label: 'Notion', icon: '📝' },
    { value: 'gmail', label: 'Gmail', icon: '📧' },
    { value: 'calendar', label: 'Google Calendar', icon: '📅' },
    { value: 'slack', label: 'Slack', icon: '💬' },
    { value: 'github', label: 'GitHub', icon: '🐙' },
    { value: 'linear', label: 'Linear', icon: '📋' },
    { value: 'figma', label: 'Figma', icon: '🎨' },
    { value: 'trello', label: 'Trello', icon: '📊' },
    { value: 'asana', label: 'Asana', icon: '✅' }
  ];

  onMount(async () => {
    await loadData();
  });

  async function loadData() {
    try {
      loading = true;
      const [integrationsData, requestsData] = await Promise.all([
        invoke('get_app_integrations'),
        invoke('get_pending_permission_requests')
      ]);
      
      integrations = integrationsData as AppIntegration[];
      pendingRequests = requestsData as PermissionRequest[];
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      loading = false;
    }
  }

  async function createIntegration() {
    try {
      const integrationId = await invoke('create_app_integration', {
        appType: newIntegration.app_type,
        name: newIntegration.name,
        description: newIntegration.description
      });

      if (newIntegration.client_id) {
        await invoke('update_integration_oauth_config', {
          integrationId,
          clientId: newIntegration.client_id,
          clientSecret: newIntegration.client_secret || null
        });
      }

      showCreateModal = false;
      newIntegration = { app_type: 'notion', name: '', description: '', client_id: '', client_secret: '' };
      await loadData();
    } catch (error) {
      console.error('Failed to create integration:', error);
      alert('Failed to create integration: ' + error);
    }
  }

  async function connectApp(integration: AppIntegration) {
    try {
      const authUrl = await invoke('generate_oauth_url', {
        integrationId: integration.id,
        agentId: null
      });
      
      // Open OAuth URL in browser
      window.open(authUrl, '_blank');
      
      // Show modal with instructions
      selectedIntegration = integration;
      showOAuthModal = true;
    } catch (error) {
      console.error('Failed to generate OAuth URL:', error);
      alert('Failed to start OAuth flow: ' + error);
    }
  }

  async function disconnectApp(integration: AppIntegration) {
    try {
      await invoke('disconnect_app_integration', {
        integrationId: integration.id
      });
      await loadData();
    } catch (error) {
      console.error('Failed to disconnect app:', error);
      alert('Failed to disconnect app: ' + error);
    }
  }

  async function respondToRequest(request: PermissionRequest, decision: string) {
    try {
      await invoke('respond_to_permission_request', {
        requestId: request.id,
        decision,
        grantedPermissions: decision === 'approve' ? request.requested_permissions : null,
        conditions: null,
        notes: null
      });
      await loadData();
    } catch (error) {
      console.error('Failed to respond to request:', error);
      alert('Failed to respond to request: ' + error);
    }
  }

  function getAppTypeInfo(appType: any) {
    const typeStr = typeof appType === 'string' ? appType : 
                   appType.Custom ? appType.Custom : 
                   Object.keys(appType)[0]?.toLowerCase();
    
    return appTypes.find(t => t.value === typeStr) || { value: typeStr, label: typeStr, icon: '🔗' };
  }

  function getStatusColor(status: any) {
    const statusStr = typeof status === 'string' ? status : Object.keys(status)[0];
    switch (statusStr) {
      case 'Connected': return 'text-green-600 bg-green-100';
      case 'Disconnected': return 'text-gray-600 bg-gray-100';
      case 'Connecting': return 'text-yellow-600 bg-yellow-100';
      case 'Error': return 'text-red-600 bg-red-100';
      case 'TokenExpired': return 'text-orange-600 bg-orange-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  }
</script>

<div class="max-w-6xl mx-auto p-6">
  <div class="flex justify-between items-center mb-8">
    <div>
      <h1 class="text-3xl font-bold text-gray-900">App Integrations</h1>
      <p class="text-gray-600 mt-2">Connect your apps to enable AI agents to access and remember information</p>
    </div>
    <button 
      on:click={() => showCreateModal = true}
      class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
    >
      + Add Integration
    </button>
  </div>

  {#if loading}
    <div class="text-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
      <p class="text-gray-600 mt-4">Loading integrations...</p>
    </div>
  {:else}
    <!-- Permission Requests Section -->
    {#if pendingRequests.length > 0}
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-yellow-800 mb-4">
          🔔 Pending Permission Requests ({pendingRequests.length})
        </h2>
        <div class="space-y-4">
          {#each pendingRequests as request}
            <div class="bg-white border border-yellow-200 rounded-lg p-4">
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-2">
                    <span class="text-lg">{getAppTypeInfo(request.app_type).icon}</span>
                    <h3 class="font-semibold">{request.agent_name}</h3>
                    <span class="text-sm text-gray-500">wants access to {getAppTypeInfo(request.app_type).label}</span>
                  </div>
                  <p class="text-gray-700 mb-2">{request.justification}</p>
                  <div class="text-sm text-gray-600">
                    <strong>Permissions:</strong> {request.requested_permissions.join(', ')}
                  </div>
                </div>
                <div class="flex gap-2 ml-4">
                  <button 
                    on:click={() => respondToRequest(request, 'approve')}
                    class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700"
                  >
                    Approve
                  </button>
                  <button 
                    on:click={() => respondToRequest(request, 'deny')}
                    class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
                  >
                    Deny
                  </button>
                </div>
              </div>
            </div>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Integrations Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {#each integrations as integration}
        <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
          <div class="flex items-center gap-3 mb-4">
            <span class="text-2xl">{getAppTypeInfo(integration.app_type).icon}</span>
            <div>
              <h3 class="font-semibold text-lg">{integration.name}</h3>
              <p class="text-sm text-gray-600">{getAppTypeInfo(integration.app_type).label}</p>
            </div>
          </div>
          
          <p class="text-gray-700 mb-4">{integration.description}</p>
          
          <div class="flex items-center justify-between mb-4">
            <span class={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(integration.connection_status)}`}>
              {typeof integration.connection_status === 'string' ? integration.connection_status : Object.keys(integration.connection_status)[0]}
            </span>
            <span class="text-xs text-gray-500">
              Created {new Date(integration.created_at).toLocaleDateString()}
            </span>
          </div>
          
          <div class="flex gap-2">
            {#if typeof integration.connection_status === 'string' && integration.connection_status === 'Connected' || Object.keys(integration.connection_status)[0] === 'Connected'}
              <button 
                on:click={() => disconnectApp(integration)}
                class="flex-1 bg-red-100 text-red-700 px-3 py-2 rounded hover:bg-red-200 transition-colors"
              >
                Disconnect
              </button>
            {:else}
              <button 
                on:click={() => connectApp(integration)}
                class="flex-1 bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 transition-colors"
              >
                Connect
              </button>
            {/if}
          </div>
        </div>
      {/each}
    </div>

    {#if integrations.length === 0}
      <div class="text-center py-12">
        <div class="text-6xl mb-4">🔗</div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">No integrations yet</h3>
        <p class="text-gray-600 mb-6">Connect your first app to get started with AI-powered memory collection</p>
        <button 
          on:click={() => showCreateModal = true}
          class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Add Your First Integration
        </button>
      </div>
    {/if}
  {/if}
</div>

<!-- Create Integration Modal -->
{#if showCreateModal}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md">
      <h2 class="text-xl font-semibold mb-4">Add New Integration</h2>
      
      <form on:submit|preventDefault={createIntegration} class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">App Type</label>
          <select bind:value={newIntegration.app_type} class="w-full border border-gray-300 rounded-lg px-3 py-2">
            {#each appTypes as appType}
              <option value={appType.value}>{appType.icon} {appType.label}</option>
            {/each}
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
          <input 
            type="text" 
            bind:value={newIntegration.name}
            placeholder="My Notion Workspace"
            class="w-full border border-gray-300 rounded-lg px-3 py-2"
            required
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea 
            bind:value={newIntegration.description}
            placeholder="Personal workspace for project notes"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 h-20"
            required
          ></textarea>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">OAuth Client ID (Optional)</label>
          <input 
            type="text" 
            bind:value={newIntegration.client_id}
            placeholder="Configure later if needed"
            class="w-full border border-gray-300 rounded-lg px-3 py-2"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">OAuth Client Secret (Optional)</label>
          <input 
            type="password" 
            bind:value={newIntegration.client_secret}
            placeholder="Configure later if needed"
            class="w-full border border-gray-300 rounded-lg px-3 py-2"
          />
        </div>
        
        <div class="flex gap-3 pt-4">
          <button 
            type="button"
            on:click={() => showCreateModal = false}
            class="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50"
          >
            Cancel
          </button>
          <button 
            type="submit"
            class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Create
          </button>
        </div>
      </form>
    </div>
  </div>
{/if}

<!-- OAuth Instructions Modal -->
{#if showOAuthModal && selectedIntegration}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md">
      <h2 class="text-xl font-semibold mb-4">OAuth Authorization</h2>
      
      <div class="space-y-4">
        <p class="text-gray-700">
          A new browser window has opened for {selectedIntegration.name} authorization.
        </p>
        
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 class="font-medium text-blue-900 mb-2">Next Steps:</h3>
          <ol class="list-decimal list-inside text-sm text-blue-800 space-y-1">
            <li>Complete the authorization in the new window</li>
            <li>Grant the requested permissions</li>
            <li>You'll be redirected back automatically</li>
            <li>Close this dialog and refresh to see the connection status</li>
          </ol>
        </div>
        
        <button 
          on:click={() => { showOAuthModal = false; selectedIntegration = null; }}
          class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          Got it
        </button>
      </div>
    </div>
  </div>
{/if}
