<script lang="ts">
  import { onMount } from 'svelte';
  import { invoke } from '@tauri-apps/api/core';
  import EnhancedAgentChat from '../../lib/components/EnhancedAgentChat.svelte';

  interface AppIntegration {
    id: string;
    app_type: any;
    name: string;
    description: string;
    connection_status: any;
  }

  interface PermissionRequest {
    id: string;
    agent_id: string;
    agent_name: string;
    app_type: any;
    requested_permissions: string[];
    justification: string;
    status: string;
    created_at: string;
  }

  interface Agent {
    agent_id: string;
    name: string;
    description?: string;
    enabled: boolean;
  }

  let integrations: AppIntegration[] = [];
  let pendingRequests: PermissionRequest[] = [];
  let agents: Agent[] = [];
  let selectedAgent: string = '';
  let loading = true;
  let demoStep = 0;

  const demoSteps = [
    {
      title: "🚀 Welcome to MindMesh App Integration Demo",
      description: "This demo shows how AI agents can request permissions to access your connected apps and remember information from them.",
      action: "Get Started"
    },
    {
      title: "📱 Step 1: Connected Apps",
      description: "First, let's see what apps are available for integration. In a real setup, you'd connect these via OAuth.",
      action: "View Apps"
    },
    {
      title: "🤖 Step 2: Meet Your Agents",
      description: "These AI agents can help you with different tasks. Each agent can request permission to access specific apps.",
      action: "View Agents"
    },
    {
      title: "💬 Step 3: Agent Requests Permission",
      description: "Chat with an agent and see how they can request access to apps when they need more information.",
      action: "Start Chat"
    },
    {
      title: "🔐 Step 4: Manage Permissions",
      description: "Review and approve/deny permission requests from your agents.",
      action: "Manage Permissions"
    }
  ];

  onMount(async () => {
    await loadData();
  });

  async function loadData() {
    try {
      loading = true;
      const [integrationsData, requestsData, agentsData] = await Promise.all([
        invoke('get_app_integrations'),
        invoke('get_pending_permission_requests'),
        invoke('get_all_agents')
      ]);
      
      integrations = integrationsData as AppIntegration[];
      pendingRequests = requestsData as PermissionRequest[];
      agents = agentsData as Agent[];

      // Create demo integrations if none exist
      if (integrations.length === 0) {
        await createDemoIntegrations();
      }

      // Create default agents if none exist
      if (agents.length === 0) {
        await invoke('create_default_agents');
        const newAgents = await invoke('get_all_agents');
        agents = newAgents as Agent[];
      }
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      loading = false;
    }
  }

  async function createDemoIntegrations() {
    const demoApps = [
      { type: 'notion', name: 'My Notion Workspace', description: 'Personal notes and project documentation' },
      { type: 'gmail', name: 'Work Gmail', description: 'Professional email account' },
      { type: 'calendar', name: 'Google Calendar', description: 'Meeting and event scheduling' },
      { type: 'slack', name: 'Team Slack', description: 'Team communication workspace' }
    ];

    for (const app of demoApps) {
      try {
        await invoke('create_app_integration', {
          appType: app.type,
          name: app.name,
          description: app.description
        });
      } catch (error) {
        console.error(`Failed to create ${app.name}:`, error);
      }
    }

    // Reload integrations
    const newIntegrations = await invoke('get_app_integrations');
    integrations = newIntegrations as AppIntegration[];
  }

  async function respondToRequest(request: PermissionRequest, decision: string) {
    try {
      await invoke('respond_to_permission_request', {
        requestId: request.id,
        decision,
        grantedPermissions: decision === 'approve' ? request.requested_permissions : null,
        conditions: null,
        notes: null
      });
      await loadData();
    } catch (error) {
      console.error('Failed to respond to request:', error);
    }
  }

  function getAppTypeInfo(appType: any) {
    const typeStr = typeof appType === 'string' ? appType : 
                   appType.Custom ? appType.Custom : 
                   Object.keys(appType)[0]?.toLowerCase();
    
    const appTypes = [
      { value: 'notion', label: 'Notion', icon: '📝' },
      { value: 'gmail', label: 'Gmail', icon: '📧' },
      { value: 'calendar', label: 'Google Calendar', icon: '📅' },
      { value: 'slack', label: 'Slack', icon: '💬' },
      { value: 'github', label: 'GitHub', icon: '🐙' },
      { value: 'linear', label: 'Linear', icon: '📋' }
    ];
    
    return appTypes.find(t => t.value === typeStr) || { value: typeStr, label: typeStr, icon: '🔗' };
  }

  function nextStep() {
    if (demoStep < demoSteps.length - 1) {
      demoStep++;
    }
  }

  function prevStep() {
    if (demoStep > 0) {
      demoStep--;
    }
  }
</script>

<div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
  <div class="max-w-6xl mx-auto">
    <!-- Demo Progress -->
    <div class="mb-8">
      <div class="flex items-center justify-between mb-4">
        <h1 class="text-3xl font-bold text-white">MindMesh Integration Demo</h1>
        <div class="text-white text-sm">
          Step {demoStep + 1} of {demoSteps.length}
        </div>
      </div>
      
      <!-- Progress Bar -->
      <div class="w-full bg-white/20 rounded-full h-2 mb-6">
        <div 
          class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500"
          style="width: {((demoStep + 1) / demoSteps.length) * 100}%"
        ></div>
      </div>

      <!-- Current Step -->
      <div class="bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl p-6 mb-6">
        <h2 class="text-2xl font-bold text-white mb-3">{demoSteps[demoStep].title}</h2>
        <p class="text-purple-200 mb-4">{demoSteps[demoStep].description}</p>
        
        <div class="flex gap-3">
          {#if demoStep > 0}
            <button 
              on:click={prevStep}
              class="bg-white/20 text-white px-4 py-2 rounded-lg hover:bg-white/30 transition-colors"
            >
              ← Previous
            </button>
          {/if}
          {#if demoStep < demoSteps.length - 1}
            <button 
              on:click={nextStep}
              class="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 rounded-lg hover:from-purple-600 hover:to-pink-600 transition-colors"
            >
              {demoSteps[demoStep].action} →
            </button>
          {/if}
        </div>
      </div>
    </div>

    {#if loading}
      <div class="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto"></div>
        <p class="text-white mt-4">Setting up demo...</p>
      </div>
    {:else}
      <!-- Step Content -->
      {#if demoStep === 1}
        <!-- Apps Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {#each integrations as integration}
            <div class="bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl p-6">
              <div class="flex items-center gap-3 mb-4">
                <span class="text-2xl">{getAppTypeInfo(integration.app_type).icon}</span>
                <div>
                  <h3 class="font-semibold text-white">{integration.name}</h3>
                  <p class="text-sm text-purple-200">{getAppTypeInfo(integration.app_type).label}</p>
                </div>
              </div>
              <p class="text-purple-200 text-sm mb-4">{integration.description}</p>
              <div class="bg-yellow-500/20 text-yellow-300 px-3 py-1 rounded-full text-xs">
                Demo Mode - Not Connected
              </div>
            </div>
          {/each}
        </div>
      {/if}

      {#if demoStep === 2}
        <!-- Agents Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          {#each agents as agent}
            <div class="bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl p-6">
              <div class="flex items-center gap-3 mb-3">
                <span class="text-2xl">{agent.enabled ? '🟢' : '🔴'}</span>
                <div>
                  <h3 class="font-semibold text-white text-lg">{agent.name}</h3>
                  <p class="text-sm text-purple-300">({agent.agent_id})</p>
                </div>
              </div>
              {#if agent.description}
                <p class="text-purple-200 text-sm mb-4">{agent.description}</p>
              {/if}
              <button 
                on:click={() => { selectedAgent = agent.agent_id; nextStep(); }}
                class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Chat with {agent.name}
              </button>
            </div>
          {/each}
        </div>
      {/if}

      {#if demoStep === 3 && selectedAgent}
        <!-- Agent Chat -->
        <div class="max-w-4xl mx-auto">
          {@const agent = agents.find(a => a.agent_id === selectedAgent)}
          {#if agent}
            <EnhancedAgentChat agentId={agent.agent_id} agentName={agent.name} />
            
            <div class="mt-6 bg-blue-500/20 border border-blue-500/30 rounded-lg p-4">
              <h4 class="font-semibold text-blue-300 mb-2">💡 Try asking:</h4>
              <ul class="text-blue-200 text-sm space-y-1">
                <li>• "Can you help me with my Notion notes?"</li>
                <li>• "What's in my Gmail inbox?"</li>
                <li>• "Show me my calendar for today"</li>
                <li>• "What are the latest Slack messages?"</li>
              </ul>
              <p class="text-blue-200 text-xs mt-2">
                The agent will suggest requesting permissions for apps it doesn't have access to.
              </p>
            </div>
          {/if}
        </div>
      {/if}

      {#if demoStep === 4}
        <!-- Permission Requests -->
        <div class="bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl p-6">
          <h3 class="text-xl font-semibold text-white mb-4">
            🔔 Permission Requests ({pendingRequests.length})
          </h3>
          
          {#if pendingRequests.length === 0}
            <div class="text-center py-8">
              <div class="text-4xl mb-4">✅</div>
              <h4 class="text-lg font-medium text-white mb-2">No pending requests</h4>
              <p class="text-purple-200">
                Go back to step 3 and ask an agent about an app to generate a permission request!
              </p>
              <button 
                on:click={() => demoStep = 2}
                class="mt-4 bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
              >
                ← Back to Agents
              </button>
            </div>
          {:else}
            <div class="space-y-4">
              {#each pendingRequests as request}
                <div class="bg-white/5 border border-white/10 rounded-lg p-4">
                  <div class="flex justify-between items-start">
                    <div class="flex-1">
                      <div class="flex items-center gap-2 mb-2">
                        <span class="text-lg">{getAppTypeInfo(request.app_type).icon}</span>
                        <h4 class="font-semibold text-white">{request.agent_name}</h4>
                        <span class="text-sm text-purple-300">
                          wants access to {getAppTypeInfo(request.app_type).label}
                        </span>
                      </div>
                      <p class="text-purple-200 mb-2">{request.justification}</p>
                      <div class="text-sm text-purple-300">
                        <strong>Permissions:</strong> {request.requested_permissions.join(', ')}
                      </div>
                    </div>
                    <div class="flex gap-2 ml-4">
                      <button 
                        on:click={() => respondToRequest(request, 'approve')}
                        class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700"
                      >
                        Approve
                      </button>
                      <button 
                        on:click={() => respondToRequest(request, 'deny')}
                        class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
                      >
                        Deny
                      </button>
                    </div>
                  </div>
                </div>
              {/each}
            </div>
          {/if}
        </div>
      {/if}
    {/if}

    <!-- Demo Complete -->
    {#if demoStep === demoSteps.length - 1 && !loading}
      <div class="mt-8 bg-gradient-to-r from-green-500/20 to-blue-500/20 border border-green-500/30 rounded-xl p-6 text-center">
        <div class="text-4xl mb-4">🎉</div>
        <h3 class="text-2xl font-bold text-white mb-3">Demo Complete!</h3>
        <p class="text-green-200 mb-4">
          You've seen how MindMesh enables AI agents to request app permissions and access your data intelligently.
        </p>
        <div class="flex gap-4 justify-center">
          <button 
            on:click={() => demoStep = 0}
            class="bg-white/20 text-white px-4 py-2 rounded-lg hover:bg-white/30 transition-colors"
          >
            Restart Demo
          </button>
          <a 
            href="/"
            class="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 rounded-lg hover:from-purple-600 hover:to-pink-600 transition-colors"
          >
            Go to Dashboard
          </a>
        </div>
      </div>
    {/if}
  </div>
</div>
