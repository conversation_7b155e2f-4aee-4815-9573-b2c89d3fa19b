<script lang="ts">
  import { onMount } from 'svelte';
  import { invoke } from '@tauri-apps/api/core';

  interface PermissionRequest {
    id: string;
    agent_id: string;
    agent_name: string;
    app_type: any;
    requested_permissions: string[];
    justification: string;
    status: string;
    created_at: string;
    expires_at: string;
    user_response?: {
      decision: string;
      granted_permissions: string[];
      conditions: string[];
      notes?: string;
      responded_at: string;
    };
  }

  interface Agent {
    agent_id: string;
    name: string;
    description?: string;
    enabled: boolean;
  }

  let pendingRequests: PermissionRequest[] = [];
  let allAgents: Agent[] = [];
  let selectedAgent: string = '';
  let agentHistory: PermissionRequest[] = [];
  let loading = true;
  let showDetailsModal = false;
  let selectedRequest: PermissionRequest | null = null;
  let responseForm = {
    decision: 'approve',
    conditions: '',
    notes: ''
  };

  const appTypes = [
    { value: 'notion', label: 'Notion', icon: '📝' },
    { value: 'gmail', label: 'Gmail', icon: '📧' },
    { value: 'calendar', label: 'Google Calendar', icon: '📅' },
    { value: 'slack', label: 'Slack', icon: '💬' },
    { value: 'github', label: 'GitHub', icon: '🐙' },
    { value: 'linear', label: 'Linear', icon: '📋' }
  ];

  onMount(async () => {
    await loadData();
  });

  async function loadData() {
    try {
      loading = true;
      const [requestsData, agentsData] = await Promise.all([
        invoke('get_pending_permission_requests'),
        invoke('get_all_agents')
      ]);
      
      pendingRequests = requestsData as PermissionRequest[];
      allAgents = agentsData as Agent[];
      
      // Cleanup expired requests
      await invoke('cleanup_expired_permission_requests');
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      loading = false;
    }
  }

  async function loadAgentHistory(agentId: string) {
    try {
      const history = await invoke('get_agent_permission_history', { agentId });
      agentHistory = history as PermissionRequest[];
    } catch (error) {
      console.error('Failed to load agent history:', error);
    }
  }

  async function respondToRequest(request: PermissionRequest, decision: string, conditions?: string[], notes?: string) {
    try {
      await invoke('respond_to_permission_request', {
        requestId: request.id,
        decision,
        grantedPermissions: decision === 'approve' ? request.requested_permissions : null,
        conditions: conditions || null,
        notes: notes || null
      });
      
      await loadData();
      if (selectedAgent) {
        await loadAgentHistory(selectedAgent);
      }
    } catch (error) {
      console.error('Failed to respond to request:', error);
      alert('Failed to respond to request: ' + error);
    }
  }

  function openDetailsModal(request: PermissionRequest) {
    selectedRequest = request;
    responseForm = {
      decision: 'approve',
      conditions: '',
      notes: ''
    };
    showDetailsModal = true;
  }

  async function submitResponse() {
    if (!selectedRequest) return;
    
    const conditions = responseForm.conditions.split('\n').filter(c => c.trim());
    await respondToRequest(
      selectedRequest, 
      responseForm.decision, 
      conditions.length > 0 ? conditions : undefined,
      responseForm.notes || undefined
    );
    
    showDetailsModal = false;
    selectedRequest = null;
  }

  function getAppTypeInfo(appType: any) {
    const typeStr = typeof appType === 'string' ? appType : 
                   appType.Custom ? appType.Custom : 
                   Object.keys(appType)[0]?.toLowerCase();
    
    return appTypes.find(t => t.value === typeStr) || { value: typeStr, label: typeStr, icon: '🔗' };
  }

  function getStatusColor(status: string) {
    switch (status) {
      case 'Pending': return 'text-yellow-600 bg-yellow-100';
      case 'Approved': return 'text-green-600 bg-green-100';
      case 'Denied': return 'text-red-600 bg-red-100';
      case 'Expired': return 'text-gray-600 bg-gray-100';
      case 'Cancelled': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  }

  function formatDate(dateStr: string) {
    return new Date(dateStr).toLocaleString();
  }

  function isExpiringSoon(expiresAt: string) {
    const expiryDate = new Date(expiresAt);
    const now = new Date();
    const hoursUntilExpiry = (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60);
    return hoursUntilExpiry < 24 && hoursUntilExpiry > 0;
  }

  $: if (selectedAgent) {
    loadAgentHistory(selectedAgent);
  }
</script>

<div class="max-w-6xl mx-auto p-6">
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900">Permission Requests</h1>
    <p class="text-gray-600 mt-2">Manage agent requests for app access and permissions</p>
  </div>

  {#if loading}
    <div class="text-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
      <p class="text-gray-600 mt-4">Loading permission requests...</p>
    </div>
  {:else}
    <!-- Pending Requests Section -->
    <div class="bg-white border border-gray-200 rounded-lg mb-8">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-xl font-semibold">Pending Requests ({pendingRequests.length})</h2>
      </div>
      
      {#if pendingRequests.length === 0}
        <div class="p-8 text-center">
          <div class="text-4xl mb-4">✅</div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No pending requests</h3>
          <p class="text-gray-600">All permission requests have been processed</p>
        </div>
      {:else}
        <div class="divide-y divide-gray-200">
          {#each pendingRequests as request}
            <div class="p-6">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-3 mb-3">
                    <span class="text-xl">{getAppTypeInfo(request.app_type).icon}</span>
                    <div>
                      <h3 class="font-semibold text-lg">{request.agent_name}</h3>
                      <p class="text-sm text-gray-600">wants access to {getAppTypeInfo(request.app_type).label}</p>
                    </div>
                    {#if isExpiringSoon(request.expires_at)}
                      <span class="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                        Expires soon
                      </span>
                    {/if}
                  </div>
                  
                  <div class="mb-3">
                    <p class="text-gray-700 mb-2"><strong>Justification:</strong> {request.justification}</p>
                    <div class="text-sm text-gray-600">
                      <strong>Requested permissions:</strong>
                      <div class="flex flex-wrap gap-1 mt-1">
                        {#each request.requested_permissions as permission}
                          <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">{permission}</span>
                        {/each}
                      </div>
                    </div>
                  </div>
                  
                  <div class="text-xs text-gray-500">
                    Requested: {formatDate(request.created_at)} • 
                    Expires: {formatDate(request.expires_at)}
                  </div>
                </div>
                
                <div class="flex gap-2 ml-6">
                  <button 
                    on:click={() => openDetailsModal(request)}
                    class="bg-gray-100 text-gray-700 px-3 py-2 rounded hover:bg-gray-200 transition-colors"
                  >
                    Review
                  </button>
                  <button 
                    on:click={() => respondToRequest(request, 'approve')}
                    class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
                  >
                    Quick Approve
                  </button>
                  <button 
                    on:click={() => respondToRequest(request, 'deny')}
                    class="bg-red-600 text-white px-3 py-2 rounded hover:bg-red-700 transition-colors"
                  >
                    Deny
                  </button>
                </div>
              </div>
            </div>
          {/each}
        </div>
      {/if}
    </div>

    <!-- Agent History Section -->
    <div class="bg-white border border-gray-200 rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-semibold">Permission History</h2>
          <select 
            bind:value={selectedAgent}
            class="border border-gray-300 rounded-lg px-3 py-2"
          >
            <option value="">Select an agent</option>
            {#each allAgents as agent}
              <option value={agent.agent_id}>{agent.name}</option>
            {/each}
          </select>
        </div>
      </div>
      
      {#if selectedAgent && agentHistory.length > 0}
        <div class="divide-y divide-gray-200">
          {#each agentHistory as request}
            <div class="p-6">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-3 mb-2">
                    <span class="text-lg">{getAppTypeInfo(request.app_type).icon}</span>
                    <span class="font-medium">{getAppTypeInfo(request.app_type).label}</span>
                    <span class={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                      {request.status}
                    </span>
                  </div>
                  
                  <p class="text-gray-700 mb-2">{request.justification}</p>
                  
                  <div class="text-sm text-gray-600 mb-2">
                    <strong>Permissions:</strong> {request.requested_permissions.join(', ')}
                  </div>
                  
                  {#if request.user_response}
                    <div class="bg-gray-50 rounded-lg p-3 text-sm">
                      <div class="font-medium mb-1">Response:</div>
                      {#if request.user_response.conditions.length > 0}
                        <div class="mb-1"><strong>Conditions:</strong> {request.user_response.conditions.join(', ')}</div>
                      {/if}
                      {#if request.user_response.notes}
                        <div class="mb-1"><strong>Notes:</strong> {request.user_response.notes}</div>
                      {/if}
                      <div class="text-xs text-gray-500">
                        Responded: {formatDate(request.user_response.responded_at)}
                      </div>
                    </div>
                  {/if}
                </div>
                
                <div class="text-xs text-gray-500 ml-4">
                  {formatDate(request.created_at)}
                </div>
              </div>
            </div>
          {/each}
        </div>
      {:else if selectedAgent}
        <div class="p-8 text-center">
          <div class="text-4xl mb-4">📋</div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No permission history</h3>
          <p class="text-gray-600">This agent hasn't made any permission requests yet</p>
        </div>
      {:else}
        <div class="p-8 text-center">
          <div class="text-4xl mb-4">👆</div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Select an agent</h3>
          <p class="text-gray-600">Choose an agent from the dropdown to view their permission history</p>
        </div>
      {/if}
    </div>
  {/if}
</div>

<!-- Request Details Modal -->
{#if showDetailsModal && selectedRequest}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
      <h2 class="text-xl font-semibold mb-4">Permission Request Details</h2>
      
      <div class="space-y-4 mb-6">
        <div class="flex items-center gap-3">
          <span class="text-2xl">{getAppTypeInfo(selectedRequest.app_type).icon}</span>
          <div>
            <h3 class="font-semibold text-lg">{selectedRequest.agent_name}</h3>
            <p class="text-gray-600">wants access to {getAppTypeInfo(selectedRequest.app_type).label}</p>
          </div>
        </div>
        
        <div>
          <h4 class="font-medium mb-2">Justification:</h4>
          <p class="text-gray-700 bg-gray-50 p-3 rounded">{selectedRequest.justification}</p>
        </div>
        
        <div>
          <h4 class="font-medium mb-2">Requested Permissions:</h4>
          <div class="flex flex-wrap gap-2">
            {#each selectedRequest.requested_permissions as permission}
              <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded">{permission}</span>
            {/each}
          </div>
        </div>
        
        <div class="text-sm text-gray-600 bg-gray-50 p-3 rounded">
          <div><strong>Request ID:</strong> {selectedRequest.id}</div>
          <div><strong>Created:</strong> {formatDate(selectedRequest.created_at)}</div>
          <div><strong>Expires:</strong> {formatDate(selectedRequest.expires_at)}</div>
        </div>
      </div>
      
      <form on:submit|preventDefault={submitResponse} class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Decision</label>
          <select bind:value={responseForm.decision} class="w-full border border-gray-300 rounded-lg px-3 py-2">
            <option value="approve">Approve</option>
            <option value="approve_with_conditions">Approve with Conditions</option>
            <option value="deny">Deny</option>
          </select>
        </div>
        
        {#if responseForm.decision === 'approve_with_conditions'}
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Conditions (one per line)</label>
            <textarea 
              bind:value={responseForm.conditions}
              placeholder="Only weekday emails&#10;No sensitive data&#10;Limited to 100 items per sync"
              class="w-full border border-gray-300 rounded-lg px-3 py-2 h-24"
            ></textarea>
          </div>
        {/if}
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Notes (optional)</label>
          <textarea 
            bind:value={responseForm.notes}
            placeholder="Additional notes about this decision..."
            class="w-full border border-gray-300 rounded-lg px-3 py-2 h-20"
          ></textarea>
        </div>
        
        <div class="flex gap-3 pt-4">
          <button 
            type="button"
            on:click={() => { showDetailsModal = false; selectedRequest = null; }}
            class="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50"
          >
            Cancel
          </button>
          <button 
            type="submit"
            class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Submit Response
          </button>
        </div>
      </form>
    </div>
  </div>
{/if}
