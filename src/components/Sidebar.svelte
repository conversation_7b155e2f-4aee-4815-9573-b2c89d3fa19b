<script lang="ts">
  export let activeSection: string;
  export let setSection: (s: string) => void;
</script>

<aside class="hidden md:flex md:flex-col w-64 bg-slate-900 border-r border-white/10 text-purple-200 shadow-xl">
  <div class="h-20 flex items-center justify-center text-2xl font-bold border-b border-white/10">
    🧠 MindMesh
  </div>
  <nav class="flex-1 overflow-y-auto py-6 px-4 space-y-2 text-sm font-medium">
    <button
      class="w-full flex items-center gap-3 px-4 py-3 rounded-lg hover:bg-white/10 transition-colors"
      class:bg-purple-600={activeSection === 'memories'}
      on:click={() => setSection('memories')}
    >
      💭 Memories
    </button>
    <button
      class="w-full flex items-center gap-3 px-4 py-3 rounded-lg hover:bg-white/10 transition-colors"
      class:bg-purple-600={activeSection === 'agents'}
      on:click={() => setSection('agents')}
    >
      🤖 Agents
    </button>
    <button
      class="w-full flex items-center gap-3 px-4 py-3 rounded-lg hover:bg-white/10 transition-colors"
      class:bg-purple-600={activeSection === 'explore'}
      on:click={() => setSection('explore')}
    >
      🔍 Explore
    </button>
    <button
      class="w-full flex items-center gap-3 px-4 py-3 rounded-lg hover:bg-white/10 transition-colors"
      class:bg-purple-600={activeSection === 'integrations'}
      on:click={() => setSection('integrations')}
    >
      🔗 Integrations
    </button>
    <button
      class="w-full flex items-center gap-3 px-4 py-3 rounded-lg hover:bg-white/10 transition-colors"
      class:bg-purple-600={activeSection === 'permissions'}
      on:click={() => setSection('permissions')}
    >
      🔐 Permissions
    </button>
  </nav>
  <footer class="p-4 text-xs text-center opacity-60 border-t border-white/10">© {new Date().getFullYear()} MindMesh</footer>
</aside>

<style>
  button.bg-purple-600 {
    background-color: rgba(147, 51, 234, 0.25);
    color: #fff;
  }
</style> 