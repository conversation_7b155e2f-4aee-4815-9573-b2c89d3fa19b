use std::collections::HashMap;
use std::path::PathBuf;
use std::fs;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use reqwest::Client;
use url::Url;
use crate::app_integrations::{AppIntegration, AppConnection, AppType, ConnectionStatus};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OAuthState {
    pub state_id: String,
    pub integration_id: String,
    pub agent_id: Option<String>, // Which agent requested this connection
    pub created_at: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TokenResponse {
    pub access_token: String,
    pub refresh_token: Option<String>,
    pub token_type: String,
    pub expires_in: Option<u64>,
    pub scope: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OAuthError {
    pub error: String,
    pub error_description: Option<String>,
}

pub struct OAuthManager {
    vault_path: PathBuf,
    integrations: HashMap<String, AppIntegration>,
    connections: HashMap<String, AppConnection>,
    oauth_states: HashMap<String, OAuthState>,
    http_client: Client,
}

impl OAuthManager {
    pub fn new(vault_path: PathBuf) -> Result<Self, String> {
        let integrations_path = vault_path.join("config").join("integrations.json");
        let connections_path = vault_path.join("config").join("connections.json");
        
        let integrations = if integrations_path.exists() {
            let content = fs::read_to_string(&integrations_path).map_err(|e| e.to_string())?;
            serde_json::from_str(&content).unwrap_or_default()
        } else {
            HashMap::new()
        };

        let connections = if connections_path.exists() {
            let content = fs::read_to_string(&connections_path).map_err(|e| e.to_string())?;
            serde_json::from_str(&content).unwrap_or_default()
        } else {
            HashMap::new()
        };

        Ok(OAuthManager {
            vault_path,
            integrations,
            connections,
            oauth_states: HashMap::new(),
            http_client: Client::new(),
        })
    }

    pub fn create_integration(&mut self, app_type: AppType, name: String, description: String) -> Result<String, String> {
        let integration = AppIntegration::new(app_type, name, description);
        let integration_id = integration.id.clone();
        
        self.integrations.insert(integration_id.clone(), integration);
        self.save_integrations()?;
        
        Ok(integration_id)
    }

    pub fn get_integration(&self, integration_id: &str) -> Option<&AppIntegration> {
        self.integrations.get(integration_id)
    }

    pub fn get_all_integrations(&self) -> Vec<&AppIntegration> {
        self.integrations.values().collect()
    }

    pub fn update_integration_oauth_config(&mut self, integration_id: &str, client_id: String, client_secret: Option<String>) -> Result<(), String> {
        if let Some(integration) = self.integrations.get_mut(integration_id) {
            integration.oauth_config.client_id = client_id;
            integration.oauth_config.client_secret = client_secret;
            integration.updated_at = Utc::now();
            self.save_integrations()?;
            Ok(())
        } else {
            Err("Integration not found".to_string())
        }
    }

    pub fn generate_auth_url(&mut self, integration_id: &str, agent_id: Option<String>) -> Result<String, String> {
        let integration = self.integrations.get(integration_id)
            .ok_or("Integration not found")?;

        if integration.oauth_config.client_id.is_empty() {
            return Err("OAuth client ID not configured".to_string());
        }

        // Generate state for CSRF protection
        let state_id = uuid::Uuid::new_v4().to_string();
        let oauth_state = OAuthState {
            state_id: state_id.clone(),
            integration_id: integration_id.to_string(),
            agent_id,
            created_at: Utc::now(),
            expires_at: Utc::now() + chrono::Duration::minutes(10), // 10 minute expiry
        };

        self.oauth_states.insert(state_id.clone(), oauth_state);

        // Build authorization URL
        let mut auth_url = Url::parse(&integration.oauth_config.auth_url)
            .map_err(|e| format!("Invalid auth URL: {}", e))?;

        auth_url.query_pairs_mut()
            .append_pair("client_id", &integration.oauth_config.client_id)
            .append_pair("redirect_uri", &integration.oauth_config.redirect_uri)
            .append_pair("scope", &integration.oauth_config.scopes.join(" "))
            .append_pair("state", &state_id)
            .append_pair("response_type", "code");

        // Add app-specific parameters
        match integration.app_type {
            AppType::Slack => {
                auth_url.query_pairs_mut()
                    .append_pair("user_scope", "");
            },
            AppType::Notion => {
                auth_url.query_pairs_mut()
                    .append_pair("owner", "user");
            },
            _ => {}
        }

        Ok(auth_url.to_string())
    }

    pub async fn handle_oauth_callback(&mut self, code: String, state: String) -> Result<String, String> {
        // Validate state
        let oauth_state = self.oauth_states.remove(&state)
            .ok_or("Invalid or expired OAuth state")?;

        if oauth_state.expires_at < Utc::now() {
            return Err("OAuth state expired".to_string());
        }

        // Get integration data without mutable borrow
        let integration_data = {
            let integration = self.integrations.get(&oauth_state.integration_id)
                .ok_or("Integration not found")?;
            integration.clone()
        };

        // Exchange code for token
        let token_response = self.exchange_code_for_token(&integration_data, &code).await?;

        // Create connection
        let connection = AppConnection {
            integration_id: oauth_state.integration_id.clone(),
            access_token: token_response.access_token,
            refresh_token: token_response.refresh_token,
            token_expires_at: token_response.expires_in.map(|expires_in| {
                Utc::now() + chrono::Duration::seconds(expires_in as i64)
            }),
            user_info: HashMap::new(),
            connected_at: Utc::now(),
        };

        // Fetch user info
        let user_info = self.fetch_user_info(&integration_data, &connection).await.unwrap_or_default();
        let mut connection = connection;
        connection.user_info = user_info;

        // Update integration status
        if let Some(integration) = self.integrations.get_mut(&oauth_state.integration_id) {
            integration.update_connection_status(ConnectionStatus::Connected);
            integration.last_sync = Some(Utc::now());
        }

        // Save connection
        self.connections.insert(oauth_state.integration_id.clone(), connection);
        self.save_integrations()?;
        self.save_connections()?;

        Ok(oauth_state.integration_id)
    }

    async fn exchange_code_for_token(&self, integration: &AppIntegration, code: &str) -> Result<TokenResponse, String> {
        let mut params = HashMap::new();
        params.insert("grant_type", "authorization_code");
        params.insert("code", code);
        params.insert("redirect_uri", &integration.oauth_config.redirect_uri);
        params.insert("client_id", &integration.oauth_config.client_id);

        if let Some(client_secret) = &integration.oauth_config.client_secret {
            params.insert("client_secret", client_secret);
        }

        let response = self.http_client
            .post(&integration.oauth_config.token_url)
            .form(&params)
            .send()
            .await
            .map_err(|e| format!("Token exchange failed: {}", e))?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(format!("Token exchange failed: {}", error_text));
        }

        let token_response: TokenResponse = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse token response: {}", e))?;

        Ok(token_response)
    }

    async fn fetch_user_info(&self, integration: &AppIntegration, connection: &AppConnection) -> Result<HashMap<String, serde_json::Value>, String> {
        let user_info_url = match integration.app_type {
            AppType::Gmail | AppType::GoogleCalendar => "https://www.googleapis.com/oauth2/v2/userinfo",
            AppType::Slack => "https://slack.com/api/auth.test",
            AppType::GitHub => "https://api.github.com/user",
            AppType::Notion => "https://api.notion.com/v1/users/me",
            _ => return Ok(HashMap::new()),
        };

        let response = self.http_client
            .get(user_info_url)
            .bearer_auth(&connection.access_token)
            .send()
            .await
            .map_err(|e| format!("Failed to fetch user info: {}", e))?;

        if !response.status().is_success() {
            return Err("Failed to fetch user info".to_string());
        }

        let user_info: HashMap<String, serde_json::Value> = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse user info: {}", e))?;

        Ok(user_info)
    }

    pub fn disconnect_integration(&mut self, integration_id: &str) -> Result<(), String> {
        if let Some(integration) = self.integrations.get_mut(integration_id) {
            integration.update_connection_status(ConnectionStatus::Disconnected);
            self.connections.remove(integration_id);
            self.save_integrations()?;
            self.save_connections()?;
            Ok(())
        } else {
            Err("Integration not found".to_string())
        }
    }

    pub fn get_connection(&self, integration_id: &str) -> Option<&AppConnection> {
        self.connections.get(integration_id)
    }

    pub fn is_connected(&self, integration_id: &str) -> bool {
        self.connections.contains_key(integration_id)
    }

    fn save_integrations(&self) -> Result<(), String> {
        let integrations_path = self.vault_path.join("config").join("integrations.json");
        let content = serde_json::to_string_pretty(&self.integrations).map_err(|e| e.to_string())?;
        fs::write(&integrations_path, content).map_err(|e| e.to_string())
    }

    fn save_connections(&self) -> Result<(), String> {
        let connections_path = self.vault_path.join("config").join("connections.json");
        let content = serde_json::to_string_pretty(&self.connections).map_err(|e| e.to_string())?;
        fs::write(&connections_path, content).map_err(|e| e.to_string())
    }
}
