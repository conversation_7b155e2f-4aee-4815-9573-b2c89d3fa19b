// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod vault;

use std::fs::File;
use std::io::{BufRead, BufReader};
use std::path::PathBuf;
use std::sync::Mutex;

use serde::{Deserialize, Serialize};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Builder, generate_handler, Manager, State};
use vault::{Vault, VaultEntry, AgentPermissions, AgentQueryRequest, AgentQueryResponse};

// Legacy MemoryEntry for migration
#[derive(Deserialize, Serialize, Clone)]
struct LegacyMemoryEntry {
    content: String,
    timestamp: String,
    source: String,
    author: String,
}

// Simplified response for frontend compatibility
#[derive(Serialize)]
struct MemoryResponse {
    content: String,
    timestamp: String,
    author: String,
    source: String,
    id: String,
    tags: Vec<String>,
}

#[derive(Serialize)]
struct OllamaEmbedRequest {
    model: String,
    prompt: String,
}

#[derive(Deserialize)]
struct OllamaEmbedResponse {
    embedding: Vec<f64>,
}

#[derive(Serialize)]
struct OllamaChatRequest {
    model: String,
    prompt: String,
    stream: bool,
}

#[derive(Deserialize)]
struct OllamaChatResponse {
    response: String,
}

#[derive(Serialize)]
struct MemoryWithSimilarity {
    memory: VaultEntry,
    similarity: f64,
}

// Ingestion API structures
#[derive(Deserialize)]
struct SlackIngestionRequest {
    text: String,
    channel: String,
    author: String,
    timestamp: Option<String>, // ISO 8601 format
    thread_ts: Option<String>,
    message_type: Option<String>,
}

#[derive(Deserialize)]
struct GitIngestionRequest {
    commit_message: String,
    author: String,
    repo: String,
    branch: String,
    commit_hash: String,
    timestamp: Option<String>, // ISO 8601 format
}

#[derive(Deserialize)]
struct GenericIngestionRequest {
    content: String,
    source_type: String,
    author: String,
    integration_id: Option<String>,
    metadata: Option<serde_json::Map<String, serde_json::Value>>,
    tags: Option<Vec<String>>,
    timestamp: Option<String>, // ISO 8601 format
}

#[derive(Deserialize)]
struct SearchRequest {
    source_filter: Option<String>,
    tag_filter: Option<Vec<String>>,
    author_filter: Option<String>,
    start_date: Option<String>, // ISO 8601 format
    end_date: Option<String>,   // ISO 8601 format
}

type VaultState = Mutex<Vault>;

fn get_app_data_dir(app_handle: &AppHandle) -> Result<PathBuf, String> {
    app_handle.path().app_data_dir().map_err(|e| e.to_string())
}

fn get_vault_path(app_handle: &AppHandle) -> Result<PathBuf, String> {
    let data_dir = get_app_data_dir(app_handle)?;
    Ok(data_dir.join("vault"))
}

fn initialize_vault(app_handle: &AppHandle) -> Result<Vault, String> {
    let vault_path = get_vault_path(app_handle)?;
    let mut vault = Vault::new(vault_path)?;
    
    // Check for legacy JSONL and migrate
    let legacy_path = get_app_data_dir(app_handle)?.join("memory_log.jsonl");
    if legacy_path.exists() {
        println!("Found legacy memory_log.jsonl, migrating to vault...");
        migrate_legacy_memories(&mut vault, &legacy_path)?;
    }
    
    Ok(vault)
}

fn migrate_legacy_memories(vault: &mut Vault, legacy_path: &PathBuf) -> Result<(), String> {
    if !legacy_path.exists() {
        return Ok(());
    }

    let file = File::open(legacy_path).map_err(|e| e.to_string())?;
    let reader = BufReader::new(file);

    let mut migrated_count = 0;
    for line in reader.lines() {
        let line = line.map_err(|e| e.to_string())?;
        if !line.trim().is_empty() {
            let legacy_entry: LegacyMemoryEntry = serde_json::from_str(&line)
                .map_err(|e| format!("Failed to parse legacy entry: {}", e))?;
            
            // Convert to new format
            let mut vault_entry = VaultEntry::new(
                legacy_entry.content,
                legacy_entry.author,
                legacy_entry.source,
            );
            
            // Parse and set timestamp
            if let Ok(dt) = chrono::DateTime::parse_from_rfc3339(&legacy_entry.timestamp) {
                vault_entry.timestamp = dt.with_timezone(&chrono::Utc);
            }
            
            vault.save_entry(vault_entry)?;
            migrated_count += 1;
        }
    }

    println!("Migrated {} entries from legacy format", migrated_count);
    
    // Rename legacy file to backup
    let backup_path = legacy_path.with_extension("jsonl.backup");
    std::fs::rename(legacy_path, backup_path).map_err(|e| e.to_string())?;
    
    Ok(())
}

fn cosine_similarity(a: &[f64], b: &[f64]) -> f64 {
    if a.len() != b.len() {
        return 0.0;
    }
    
    let dot_product: f64 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();
    let norm_a: f64 = a.iter().map(|x| x * x).sum::<f64>().sqrt();
    let norm_b: f64 = b.iter().map(|x| x * x).sum::<f64>().sqrt();
    
    if norm_a == 0.0 || norm_b == 0.0 {
        return 0.0;
    }
    
    dot_product / (norm_a * norm_b)
}

async fn get_embedding(text: &str) -> Result<Vec<f64>, String> {
    let client = reqwest::Client::new();
    let request = OllamaEmbedRequest {
        model: "nomic-embed-text".to_string(),
        prompt: text.to_string(),
    };
    
    let response = client
        .post("http://localhost:11434/api/embeddings")
        .json(&request)
        .send()
        .await
        .map_err(|e| format!("Failed to call Ollama: {}", e))?;
    
    if !response.status().is_success() {
        return Err(format!("Ollama API error: {}", response.status()));
    }
    
    let embed_response: OllamaEmbedResponse = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse embedding response: {}", e))?;
    
    Ok(embed_response.embedding)
}

async fn get_chat_response(prompt: &str) -> Result<String, String> {
    let client = reqwest::Client::new();
    let request = OllamaChatRequest {
        model: "phi3:mini".to_string(),
        prompt: prompt.to_string(),
        stream: false,
    };
    
    let response = client
        .post("http://localhost:11434/api/generate")
        .json(&request)
        .send()
        .await
        .map_err(|e| format!("Failed to call Ollama: {}", e))?;
    
    if !response.status().is_success() {
        return Err(format!("Ollama API error: {}", response.status()));
    }
    
    let chat_response: OllamaChatResponse = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse chat response: {}", e))?;
    
    Ok(chat_response.response)
}

#[tauri::command]
async fn query_memories(question: String, vault_state: State<'_, VaultState>) -> Result<String, String> {
    // Extract entries first, then release the lock
    let entries = {
        let vault = vault_state.lock().map_err(|e| e.to_string())?;
        vault.get_all_entries()?
    };
    
    if entries.is_empty() {
        return Ok("I don't have any memories to search through yet. Try saving some memories first!".to_string());
    }
    
    // Get embedding for the question
    let question_embedding = get_embedding(&question).await?;
    
    // Get embeddings for all memories and calculate similarities
    let mut memory_similarities = Vec::new();
    
    for entry in entries {
        // Use cached embedding if available, otherwise generate new one
        let embedding = if let Some(cached_embedding) = &entry.metadata.embedding_vector {
            cached_embedding.clone()
        } else {
            match get_embedding(&entry.content).await {
                Ok(embedding) => embedding,
                Err(_) => continue, // Skip entries that fail to embed
            }
        };
        
        let similarity = cosine_similarity(&question_embedding, &embedding);
        memory_similarities.push(MemoryWithSimilarity {
            memory: entry,
            similarity,
        });
    }
    
    // Sort by similarity (highest first) and take top 3
    memory_similarities.sort_by(|a, b| b.similarity.partial_cmp(&a.similarity).unwrap());
    let top_memories: Vec<&MemoryWithSimilarity> = memory_similarities
        .iter()
        .take(3)
        .filter(|ms| ms.similarity > 0.1) // Only include memories with some relevance
        .collect();
    
    if top_memories.is_empty() {
        return Ok("I couldn't find any relevant memories for your question.".to_string());
    }
    
    // Format the prompt for the LLM
    let mut prompt = "Based on the following memories:\n\n".to_string();
    
    for (i, memory_sim) in top_memories.iter().enumerate() {
        prompt.push_str(&format!(
            "Memory {}: {} (saved on {})\n",
            i + 1,
            memory_sim.memory.content,
            memory_sim.memory.timestamp.format("%Y-%m-%d %H:%M:%S")
        ));
    }
    
    prompt.push_str(&format!(
        "\nAnswer the question: \"{}\"\n\nBe concise and reference specific memories when possible.",
        question
    ));
    
    // Get response from LLM
    let response = get_chat_response(&prompt).await?;
    
    Ok(response)
}

#[tauri::command]
async fn save_memory(content: String, vault_state: State<'_, VaultState>) -> Result<(), String> {
    // Generate embedding for the content first
    let embedding = get_embedding(&content).await?;
    
    // Then acquire the lock to save
    {
        let mut vault = vault_state.lock().map_err(|e| e.to_string())?;
        
        // Create new vault entry
        let entry = VaultEntry::new(content, "bipin@mindmesh".to_string(), "manual".to_string())
            .with_embedding(embedding);
        
        vault.save_entry(entry)?;
    }
    
    Ok(())
}

#[tauri::command]
fn get_memories(vault_state: State<'_, VaultState>) -> Result<Vec<MemoryResponse>, String> {
    let vault = vault_state.lock().map_err(|e| e.to_string())?;
    let entries = vault.get_all_entries()?;
    
    let memories: Vec<MemoryResponse> = entries
        .into_iter()
        .map(|entry| MemoryResponse {
            id: entry.id,
            content: entry.content,
            timestamp: entry.timestamp.to_rfc3339(),
            author: entry.author,
            source: entry.source.source_type,
            tags: entry.tags,
        })
        .collect();
    
    Ok(memories)
}

#[tauri::command]
fn delete_memory_by_id(id: String, vault_state: State<'_, VaultState>) -> Result<(), String> {
    println!("DELETE_BY_ID: Received ID: '{}'", id);
    
    let mut vault = vault_state.lock().map_err(|e| e.to_string())?;
    
    // Simple and reliable - just delete by ID
    match vault.delete_entry(&id) {
        Ok(_) => {
            println!("DELETE_BY_ID: Successfully deleted entry with ID: {}", id);
            Ok(())
        },
        Err(e) => {
            let error_msg = format!("Failed to delete memory with ID '{}': {}", id, e);
            println!("DELETE_BY_ID: {}", error_msg);
            Err(error_msg)
        }
    }
}

#[tauri::command]
fn delete_memory(timestamp: String, vault_state: State<'_, VaultState>) -> Result<(), String> {
    println!("DELETE: Received timestamp: '{}'", timestamp);
    println!("DELETE: WARNING - Using timestamp-based deletion is deprecated. Use delete_memory_by_id instead.");
    
    let mut vault = vault_state.lock().map_err(|e| e.to_string())?;
    
    // Get all entries and log them for debugging
    let entries = vault.get_all_entries()?;
    println!("DELETE: Found {} total entries", entries.len());
    
    // Log all timestamps for debugging
    for (i, entry) in entries.iter().enumerate() {
        println!("DELETE: Entry {}: timestamp='{}', id='{}'", i, entry.timestamp.to_rfc3339(), entry.id);
    }
    
    // Parse the incoming timestamp for better comparison
    let parsed_ts = match chrono::DateTime::parse_from_rfc3339(&timestamp) {
        Ok(dt) => Some(dt.with_timezone(&chrono::Utc)),
        Err(e) => {
            println!("DELETE: Failed to parse timestamp '{}': {}", timestamp, e);
            None
        }
    };
    
    // Find entry using multiple strategies with tolerance
    let entry_to_delete = entries
        .into_iter()
        .find(|entry| {
            println!("DELETE: Comparing with entry ID: {}, timestamp: {}", entry.id, entry.timestamp.to_rfc3339());
            
            // Strategy 1: Exact string match
            if entry.timestamp.to_rfc3339() == timestamp {
                println!("DELETE: Found exact string match!");
                return true;
            }
            
            // Strategy 2: Parse and compare with 500ms tolerance
            if let Some(parsed_ts) = parsed_ts {
                let diff_ms = (entry.timestamp - parsed_ts).num_milliseconds().abs();
                if diff_ms < 500 {
                    println!("DELETE: Found timestamp match within {}ms tolerance!", diff_ms);
                    return true;
                }
            }
            
            // Strategy 3: ID match (in case frontend accidentally sent ID as timestamp)
            if timestamp == entry.id {
                println!("DELETE: Found ID match!");
                return true;
            }
            
            // Strategy 4: Prefix matching for truncated timestamps
            let entry_timestamp_str = entry.timestamp.to_rfc3339();
            if entry_timestamp_str.starts_with(&timestamp) {
                println!("DELETE: Found prefix match!");
                return true;
            }
            
            false
        });
    
    if let Some(entry) = entry_to_delete {
        println!("DELETE: Deleting entry with ID: {} and timestamp: {}", entry.id, entry.timestamp.to_rfc3339());
        vault.delete_entry(&entry.id)?;
        println!("DELETE: Successfully deleted entry");
        Ok(())
    } else {
        let error_msg = format!("No memory found with timestamp: {}", timestamp);
        println!("DELETE: {}", error_msg);
        Err(error_msg)
    }
}

#[tauri::command]
fn get_vault_stats(vault_state: State<'_, VaultState>) -> Result<serde_json::Value, String> {
    let vault = vault_state.lock().map_err(|e| e.to_string())?;
    let entries = vault.get_all_entries()?;
    
    let mut stats = serde_json::Map::new();
    stats.insert("total_entries".to_string(), serde_json::Value::Number(entries.len().into()));
    stats.insert("vault_version".to_string(), serde_json::Value::String(vault.config.version.clone()));
    stats.insert("tags_count".to_string(), serde_json::Value::Number(vault.index.tags.len().into()));
    
    // Count entries by source
    let mut source_counts = serde_json::Map::new();
    for entry in &entries {
        let count = source_counts
            .entry(&entry.source.source_type)
            .or_insert(serde_json::Value::Number(0.into()));
        if let Some(num) = count.as_u64() {
            *count = serde_json::Value::Number((num + 1).into());
        }
    }
    stats.insert("sources".to_string(), serde_json::Value::Object(source_counts));
    
    Ok(serde_json::Value::Object(stats))
}

// ========== INGESTION API COMMANDS ==========

#[tauri::command]
async fn ingest_slack_memory(
    request: SlackIngestionRequest,
    vault_state: State<'_, VaultState>,
) -> Result<String, String> {
    // Parse timestamp if provided
    let timestamp = if let Some(ts_str) = request.timestamp {
        chrono::DateTime::parse_from_rfc3339(&ts_str)
            .map(|dt| dt.with_timezone(&chrono::Utc))
            .ok()
    } else {
        None
    };

    // Create Slack entry
    let mut entry = VaultEntry::from_slack(
        request.text.clone(),
        request.channel,
        request.author,
        timestamp,
        request.thread_ts,
        request.message_type,
    );

    // Generate embedding first
    let embedding = get_embedding(&request.text).await?;
    entry = entry.with_embedding(embedding);

    // Then save to vault
    let entry_id = entry.id.clone();
    {
        let mut vault = vault_state.lock().map_err(|e| e.to_string())?;
        vault.save_entry(entry)?;
    }

    Ok(entry_id)
}

#[tauri::command]
async fn ingest_git_memory(
    request: GitIngestionRequest,
    vault_state: State<'_, VaultState>,
) -> Result<String, String> {
    // Parse timestamp if provided
    let timestamp = if let Some(ts_str) = request.timestamp {
        chrono::DateTime::parse_from_rfc3339(&ts_str)
            .map(|dt| dt.with_timezone(&chrono::Utc))
            .ok()
    } else {
        None
    };

    // Create Git entry
    let mut entry = VaultEntry::from_git_commit(
        request.commit_message.clone(),
        request.author,
        request.repo,
        request.branch,
        request.commit_hash,
        timestamp,
    );

    // Generate embedding first
    let embedding = get_embedding(&request.commit_message).await?;
    entry = entry.with_embedding(embedding);

    // Then save to vault
    let entry_id = entry.id.clone();
    {
        let mut vault = vault_state.lock().map_err(|e| e.to_string())?;
        vault.save_entry(entry)?;
    }

    Ok(entry_id)
}

#[tauri::command]
async fn ingest_generic_memory(
    request: GenericIngestionRequest,
    vault_state: State<'_, VaultState>,
) -> Result<String, String> {
    // Parse timestamp if provided
    let timestamp = if let Some(ts_str) = request.timestamp {
        chrono::DateTime::parse_from_rfc3339(&ts_str)
            .map(|dt| dt.with_timezone(&chrono::Utc))
            .ok()
    } else {
        None
    };

    // Convert metadata
    let metadata = request.metadata.unwrap_or_default()
        .into_iter()
        .collect();

    let tags = request.tags.unwrap_or_default();

    // Create generic entry
    let mut entry = VaultEntry::from_external_source(
        request.content.clone(),
        request.source_type,
        request.author,
        request.integration_id,
        metadata,
        tags,
    );

    if let Some(ts) = timestamp {
        entry.timestamp = ts;
    }

    // Generate embedding first
    let embedding = get_embedding(&request.content).await?;
    entry = entry.with_embedding(embedding);

    // Then save to vault
    let entry_id = entry.id.clone();
    {
        let mut vault = vault_state.lock().map_err(|e| e.to_string())?;
        vault.save_entry(entry)?;
    }

    Ok(entry_id)
}

#[tauri::command]
fn search_memories(
    request: SearchRequest,
    vault_state: State<'_, VaultState>,
) -> Result<Vec<MemoryResponse>, String> {
    let vault = vault_state.lock().map_err(|e| e.to_string())?;

    // Parse date range if provided
    let date_range = if let (Some(start_str), Some(end_str)) = (request.start_date, request.end_date) {
        let start = chrono::DateTime::parse_from_rfc3339(&start_str)
            .map_err(|e| format!("Invalid start_date format: {}", e))?
            .with_timezone(&chrono::Utc);
        let end = chrono::DateTime::parse_from_rfc3339(&end_str)
            .map_err(|e| format!("Invalid end_date format: {}", e))?
            .with_timezone(&chrono::Utc);
        Some((start, end))
    } else {
        None
    };

    // Search with filters
    let entries = vault.search_entries(
        request.source_filter,
        request.tag_filter,
        request.author_filter,
        date_range,
    )?;

    // Convert to response format
    let memories: Vec<MemoryResponse> = entries
        .into_iter()
        .map(|entry| MemoryResponse {
            id: entry.id,
            content: entry.content,
            timestamp: entry.timestamp.to_rfc3339(),
            author: entry.author,
            source: entry.source.source_type,
            tags: entry.tags,
        })
        .collect();

    Ok(memories)
}

#[tauri::command]
fn get_sources(vault_state: State<'_, VaultState>) -> Result<Vec<String>, String> {
    let vault = vault_state.lock().map_err(|e| e.to_string())?;
    vault.get_all_source_types()
}

#[tauri::command]
fn get_tags(vault_state: State<'_, VaultState>) -> Result<Vec<String>, String> {
    let vault = vault_state.lock().map_err(|e| e.to_string())?;
    Ok(vault.get_all_tags())
}

// ========== AGENT API COMMANDS ==========

#[tauri::command]
async fn agent_query(
    request: AgentQueryRequest,
    vault_state: State<'_, VaultState>,
) -> Result<AgentQueryResponse, String> {
    // Extract data from vault first, then release the lock
    let (permissions, permitted_memories) = {
        let vault = vault_state.lock().map_err(|e| e.to_string())?;
        let permissions = vault.get_agent_permissions(&request.agent_id)?
            .ok_or_else(|| format!("Agent '{}' not found", request.agent_id))?;

        if !permissions.enabled {
            return Err(format!("Agent '{}' is disabled", request.agent_id));
        }

        let permitted_memories = vault.get_permitted_memories(&request.agent_id)?;
        (permissions, permitted_memories)
    };

    if permitted_memories.is_empty() {
        return Ok(AgentQueryResponse {
            agent_id: request.agent_id,
            question: request.question,
            response: "I don't have access to any memories that match your query.".to_string(),
            memories_used: Vec::new(),
            total_memories_found: 0,
            query_timestamp: chrono::Utc::now(),
        });
    }

    // Generate embedding for the question
    let question_embedding = get_embedding(&request.question).await?;

    // Calculate similarities
    let mut memory_similarities = Vec::new();
    for entry in permitted_memories {
        if let Some(embedding) = &entry.metadata.embedding_vector {
            let similarity = cosine_similarity(&question_embedding, embedding);
            if similarity > request.min_similarity.unwrap_or(0.1) {
                memory_similarities.push((entry, similarity));
            }
        }
    }

    // Sort by similarity and take top results
    memory_similarities.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
    let max_results = request.max_results.unwrap_or(5);
    let top_memories: Vec<_> = memory_similarities.into_iter().take(max_results).collect();

    if top_memories.is_empty() {
        return Ok(AgentQueryResponse {
            agent_id: request.agent_id,
            question: request.question,
            response: "I couldn't find any relevant memories for your question.".to_string(),
            memories_used: Vec::new(),
            total_memories_found: 0,
            query_timestamp: chrono::Utc::now(),
        });
    }

    // Create memory snippets with provenance
    let memory_snippets: Vec<vault::PermissionedMemorySnippet> = top_memories.iter()
        .map(|(entry, similarity)| vault::PermissionedMemorySnippet {
            id: entry.id.clone(),
            content: entry.content.clone(),
            timestamp: entry.timestamp,
            author: entry.author.clone(),
            source: entry.source.source_type.clone(),
            tags: entry.tags.clone(),
            similarity_score: *similarity,
            provenance: vault::MemoryProvenance {
                source_type: entry.source.source_type.clone(),
                integration_id: entry.source.integration_id.clone(),
                source_metadata: entry.source.metadata.clone(),
                permissions_used: format!("agent:{}", request.agent_id),
            },
        })
        .collect();

    // Format prompt for LLM
    let mut prompt = format!(
        "You are an AI agent with ID '{}'. Based on the following memories that you have permission to access:\n\n",
        request.agent_id
    );

    for (i, snippet) in memory_snippets.iter().enumerate() {
        prompt.push_str(&format!(
            "Memory {}: {} (from {} on {})\n",
            i + 1,
            snippet.content,
            snippet.source,
            snippet.timestamp.format("%Y-%m-%d %H:%M:%S")
        ));
    }

    prompt.push_str(&format!(
        "\nAnswer the question: \"{}\"\n\nImportant: Only use information from the provided memories. Include source attribution in your response.",
        request.question
    ));

    // Get response from LLM
    let llm_response = get_chat_response(&prompt).await?;

    Ok(AgentQueryResponse {
        agent_id: request.agent_id,
        question: request.question,
        response: llm_response,
        memories_used: memory_snippets,
        total_memories_found: top_memories.len(),
        query_timestamp: chrono::Utc::now(),
    })
}

#[tauri::command]
fn get_agent_permissions(
    agent_id: String,
    vault_state: State<'_, VaultState>,
) -> Result<Option<AgentPermissions>, String> {
    let vault = vault_state.lock().map_err(|e| e.to_string())?;
    vault.get_agent_permissions(&agent_id)
}

#[tauri::command]
fn set_agent_permissions(
    permissions: AgentPermissions,
    vault_state: State<'_, VaultState>,
) -> Result<(), String> {
    let mut vault = vault_state.lock().map_err(|e| e.to_string())?;
    vault.set_agent_permissions(permissions)
}

#[tauri::command]
fn get_all_agents(vault_state: State<'_, VaultState>) -> Result<Vec<AgentPermissions>, String> {
    let vault = vault_state.lock().map_err(|e| e.to_string())?;
    vault.get_all_agents()
}

#[tauri::command]
fn delete_agent(
    agent_id: String,
    vault_state: State<'_, VaultState>,
) -> Result<(), String> {
    let mut vault = vault_state.lock().map_err(|e| e.to_string())?;
    vault.delete_agent(&agent_id)
}

#[tauri::command]
fn get_agent_permitted_memories(
    agent_id: String,
    vault_state: State<'_, VaultState>,
) -> Result<Vec<MemoryResponse>, String> {
    let vault = vault_state.lock().map_err(|e| e.to_string())?;
    let entries = vault.get_permitted_memories(&agent_id)?;
    
    let memories: Vec<MemoryResponse> = entries
        .into_iter()
        .map(|entry| MemoryResponse {
            id: entry.id,
            content: entry.content,
            timestamp: entry.timestamp.to_rfc3339(),
            author: entry.author,
            source: entry.source.source_type,
            tags: entry.tags,
        })
        .collect();
    
    Ok(memories)
}

#[tauri::command]
fn create_default_agents(vault_state: State<'_, VaultState>) -> Result<(), String> {
    let mut vault = vault_state.lock().map_err(|e| e.to_string())?;
    
    // Create onboarding bot
    let onboarding_bot = AgentPermissions {
        agent_id: "onboarding_bot".to_string(),
        name: "Onboarding Bot".to_string(),
        description: Some("Helps new users get started".to_string()),
        can_read_sources: vec!["manual".to_string(), "slack".to_string()],
        can_read_tags: vec!["onboarding".to_string(), "help".to_string(), "getting-started".to_string()],
        can_write_tags: vec!["onboarding".to_string(), "bot-created".to_string()],
        can_read_authors: vec!["slack:*".to_string(), "bipin@mindmesh".to_string()],
        max_memory_age_days: Some(30), // Only recent memories
        enabled: true,
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
    };

    // Create RevOps agent
    let revops_agent = AgentPermissions {
        agent_id: "revops_agent".to_string(),
        name: "Revenue Operations Agent".to_string(),
        description: Some("Handles revenue and sales operations".to_string()),
        can_read_sources: vec!["slack".to_string(), "email".to_string(), "git".to_string()],
        can_read_tags: vec!["sales".to_string(), "revenue".to_string(), "client".to_string(), "enterprise".to_string()],
        can_write_tags: vec!["revops".to_string(), "analysis".to_string()],
        can_read_authors: vec!["slack:*".to_string(), "git:*".to_string()],
        max_memory_age_days: Some(90), // Longer history for analysis
        enabled: true,
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
    };

    // Create support agent
    let support_agent = AgentPermissions {
        agent_id: "support_agent".to_string(),
        name: "Customer Support Agent".to_string(),
        description: Some("Helps with customer support queries".to_string()),
        can_read_sources: vec!["slack".to_string(), "email".to_string(), "manual".to_string()],
        can_read_tags: vec!["support".to_string(), "bug".to_string(), "feedback".to_string(), "customer".to_string()],
        can_write_tags: vec!["support".to_string(), "resolved".to_string()],
        can_read_authors: vec!["slack:*".to_string(), "support@*".to_string()],
        max_memory_age_days: Some(60),
        enabled: true,
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
    };

    vault.set_agent_permissions(onboarding_bot)?;
    vault.set_agent_permissions(revops_agent)?;
    vault.set_agent_permissions(support_agent)?;

    Ok(())
}

fn main() {
    Builder::default()
        .setup(|app| {
            let vault = initialize_vault(app.handle()).expect("Failed to initialize vault");
            app.manage(VaultState::new(vault));
            Ok(())
        })
        .invoke_handler(generate_handler![
            save_memory, 
            get_memories, 
            delete_memory,
            delete_memory_by_id, 
            query_memories,
            get_vault_stats,
            ingest_slack_memory,
            ingest_git_memory,
            ingest_generic_memory,
            search_memories,
            get_sources,
            get_tags,
            agent_query,
            get_agent_permissions,
            set_agent_permissions,
            get_all_agents,
            delete_agent,
            get_agent_permitted_memories,
            create_default_agents,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

