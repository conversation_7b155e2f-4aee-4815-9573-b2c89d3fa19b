use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppIntegration {
    pub id: String,
    pub app_type: AppType,
    pub name: String,
    pub description: String,
    pub oauth_config: OAuthConfig,
    pub connection_status: ConnectionStatus,
    pub permissions_granted: Vec<String>,
    pub last_sync: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AppType {
    Notion,
    Gmail,
    GoogleCalendar,
    Slack,
    GitHub,
    Linear,
    Figma,
    Trello,
    Asana,
    Custom(String),
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OAuthConfig {
    pub client_id: String,
    pub client_secret: Option<String>, // Encrypted
    pub redirect_uri: String,
    pub scopes: Vec<String>,
    pub auth_url: String,
    pub token_url: String,
    pub api_base_url: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum ConnectionStatus {
    Disconnected,
    Connecting,
    Connected,
    Error(String),
    TokenExpired,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConnection {
    pub integration_id: String,
    pub access_token: String, // Encrypted
    pub refresh_token: Option<String>, // Encrypted
    pub token_expires_at: Option<DateTime<Utc>>,
    pub user_info: HashMap<String, serde_json::Value>,
    pub connected_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncConfig {
    pub enabled: bool,
    pub sync_interval_minutes: u32,
    pub last_sync_cursor: Option<String>,
    pub filters: SyncFilters,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncFilters {
    pub date_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
    pub include_patterns: Vec<String>,
    pub exclude_patterns: Vec<String>,
    pub max_items_per_sync: Option<u32>,
}

impl AppType {
    pub fn default_oauth_config(&self) -> OAuthConfig {
        match self {
            AppType::Notion => OAuthConfig {
                client_id: "".to_string(),
                client_secret: None,
                redirect_uri: "http://localhost:3000/oauth/notion/callback".to_string(),
                scopes: vec!["read".to_string()],
                auth_url: "https://api.notion.com/v1/oauth/authorize".to_string(),
                token_url: "https://api.notion.com/v1/oauth/token".to_string(),
                api_base_url: "https://api.notion.com/v1".to_string(),
            },
            AppType::Gmail => OAuthConfig {
                client_id: "".to_string(),
                client_secret: None,
                redirect_uri: "http://localhost:3000/oauth/gmail/callback".to_string(),
                scopes: vec![
                    "https://www.googleapis.com/auth/gmail.readonly".to_string(),
                    "https://www.googleapis.com/auth/userinfo.email".to_string(),
                ],
                auth_url: "https://accounts.google.com/o/oauth2/v2/auth".to_string(),
                token_url: "https://oauth2.googleapis.com/token".to_string(),
                api_base_url: "https://gmail.googleapis.com/gmail/v1".to_string(),
            },
            AppType::GoogleCalendar => OAuthConfig {
                client_id: "".to_string(),
                client_secret: None,
                redirect_uri: "http://localhost:3000/oauth/calendar/callback".to_string(),
                scopes: vec![
                    "https://www.googleapis.com/auth/calendar.readonly".to_string(),
                    "https://www.googleapis.com/auth/userinfo.email".to_string(),
                ],
                auth_url: "https://accounts.google.com/o/oauth2/v2/auth".to_string(),
                token_url: "https://oauth2.googleapis.com/token".to_string(),
                api_base_url: "https://www.googleapis.com/calendar/v3".to_string(),
            },
            AppType::Slack => OAuthConfig {
                client_id: "".to_string(),
                client_secret: None,
                redirect_uri: "http://localhost:3000/oauth/slack/callback".to_string(),
                scopes: vec![
                    "channels:read".to_string(),
                    "channels:history".to_string(),
                    "users:read".to_string(),
                ],
                auth_url: "https://slack.com/oauth/v2/authorize".to_string(),
                token_url: "https://slack.com/api/oauth.v2.access".to_string(),
                api_base_url: "https://slack.com/api".to_string(),
            },
            AppType::GitHub => OAuthConfig {
                client_id: "".to_string(),
                client_secret: None,
                redirect_uri: "http://localhost:3000/oauth/github/callback".to_string(),
                scopes: vec!["repo".to_string(), "user:email".to_string()],
                auth_url: "https://github.com/login/oauth/authorize".to_string(),
                token_url: "https://github.com/login/oauth/access_token".to_string(),
                api_base_url: "https://api.github.com".to_string(),
            },
            AppType::Linear => OAuthConfig {
                client_id: "".to_string(),
                client_secret: None,
                redirect_uri: "http://localhost:3000/oauth/linear/callback".to_string(),
                scopes: vec!["read".to_string()],
                auth_url: "https://linear.app/oauth/authorize".to_string(),
                token_url: "https://api.linear.app/oauth/token".to_string(),
                api_base_url: "https://api.linear.app/graphql".to_string(),
            },
            _ => OAuthConfig {
                client_id: "".to_string(),
                client_secret: None,
                redirect_uri: "http://localhost:3000/oauth/callback".to_string(),
                scopes: vec![],
                auth_url: "".to_string(),
                token_url: "".to_string(),
                api_base_url: "".to_string(),
            },
        }
    }

    pub fn display_name(&self) -> &str {
        match self {
            AppType::Notion => "Notion",
            AppType::Gmail => "Gmail",
            AppType::GoogleCalendar => "Google Calendar",
            AppType::Slack => "Slack",
            AppType::GitHub => "GitHub",
            AppType::Linear => "Linear",
            AppType::Figma => "Figma",
            AppType::Trello => "Trello",
            AppType::Asana => "Asana",
            AppType::Custom(name) => name,
        }
    }

    pub fn icon(&self) -> &str {
        match self {
            AppType::Notion => "📝",
            AppType::Gmail => "📧",
            AppType::GoogleCalendar => "📅",
            AppType::Slack => "💬",
            AppType::GitHub => "🐙",
            AppType::Linear => "📋",
            AppType::Figma => "🎨",
            AppType::Trello => "📊",
            AppType::Asana => "✅",
            AppType::Custom(_) => "🔗",
        }
    }
}

impl AppIntegration {
    pub fn new(app_type: AppType, name: String, description: String) -> Self {
        let oauth_config = app_type.default_oauth_config();
        
        AppIntegration {
            id: uuid::Uuid::new_v4().to_string(),
            app_type,
            name,
            description,
            oauth_config,
            connection_status: ConnectionStatus::Disconnected,
            permissions_granted: Vec::new(),
            last_sync: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    pub fn with_oauth_config(mut self, config: OAuthConfig) -> Self {
        self.oauth_config = config;
        self.updated_at = Utc::now();
        self
    }

    pub fn update_connection_status(&mut self, status: ConnectionStatus) {
        self.connection_status = status;
        self.updated_at = Utc::now();
    }
}

impl Default for SyncConfig {
    fn default() -> Self {
        SyncConfig {
            enabled: true,
            sync_interval_minutes: 15, // Sync every 15 minutes
            last_sync_cursor: None,
            filters: SyncFilters {
                date_range: None,
                include_patterns: Vec::new(),
                exclude_patterns: Vec::new(),
                max_items_per_sync: Some(100),
            },
        }
    }
}
